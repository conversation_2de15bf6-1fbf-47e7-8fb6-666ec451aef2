# 数据库设计文档

## 数据库架构概览

本系统采用MySQL作为主数据库，设计遵循第三范式，确保数据一致性和完整性。

## 核心数据表

### 1. 用户管理相关表

#### users (用户表)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    real_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    department VARCHAR(100),
    position VARCHAR(100),
    status TINYINT DEFAULT 1 COMMENT '1:正常 0:禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### roles (角色表)
```sql
CREATE TABLE roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    permissions JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### user_roles (用户角色关联表)
```sql
CREATE TABLE user_roles (
    user_id BIGINT,
    role_id BIGINT,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (role_id) REFERENCES roles(id)
);
```

### 2. 化学品档案相关表

#### chemicals (化学品基础信息表)
```sql
CREATE TABLE chemicals (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    cas_number VARCHAR(20) UNIQUE,
    un_number VARCHAR(10),
    universal_code VARCHAR(50),
    chinese_name VARCHAR(200) NOT NULL,
    english_name VARCHAR(200),
    molecular_formula VARCHAR(100),
    molecular_weight DECIMAL(10,4),
    physical_state ENUM('solid', 'liquid', 'gas') NOT NULL,
    danger_class VARCHAR(50),
    danger_level TINYINT,
    storage_requirements TEXT,
    incompatible_chemicals JSON,
    sds_file_path VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### chemical_properties (化学品理化性质表)
```sql
CREATE TABLE chemical_properties (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    chemical_id BIGINT NOT NULL,
    property_name VARCHAR(100) NOT NULL,
    property_value TEXT,
    unit VARCHAR(20),
    FOREIGN KEY (chemical_id) REFERENCES chemicals(id)
);
```

### 3. 库存管理相关表

#### inventory (库存主表)
```sql
CREATE TABLE inventory (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    chemical_id BIGINT NOT NULL,
    batch_number VARCHAR(100) NOT NULL,
    supplier VARCHAR(200),
    purchase_date DATE,
    production_date DATE,
    expiry_date DATE,
    opened_date DATE,
    quantity DECIMAL(10,4) NOT NULL,
    unit VARCHAR(20) NOT NULL,
    location_code VARCHAR(100),
    storage_cabinet VARCHAR(50),
    storage_layer VARCHAR(10),
    storage_position VARCHAR(10),
    qr_code VARCHAR(200) UNIQUE,
    status ENUM('normal', 'expired', 'used_up', 'disposed') DEFAULT 'normal',
    responsible_person BIGINT,
    created_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (chemical_id) REFERENCES chemicals(id),
    FOREIGN KEY (responsible_person) REFERENCES users(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);
```

#### inventory_transactions (库存变动记录表)
```sql
CREATE TABLE inventory_transactions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    inventory_id BIGINT NOT NULL,
    transaction_type ENUM('in', 'out', 'return', 'transfer', 'dispose') NOT NULL,
    quantity DECIMAL(10,4) NOT NULL,
    remaining_quantity DECIMAL(10,4) NOT NULL,
    operator_id BIGINT NOT NULL,
    purpose TEXT,
    notes TEXT,
    transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (inventory_id) REFERENCES inventory(id),
    FOREIGN KEY (operator_id) REFERENCES users(id)
);
```

### 4. 危险废物管理相关表

#### waste_records (危险废物记录表)
```sql
CREATE TABLE waste_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    waste_code VARCHAR(50) NOT NULL,
    waste_name VARCHAR(200) NOT NULL,
    source_chemical_id BIGINT,
    waste_category VARCHAR(100),
    physical_state ENUM('solid', 'liquid', 'gas', 'mixed'),
    quantity DECIMAL(10,4) NOT NULL,
    unit VARCHAR(20) NOT NULL,
    container_type VARCHAR(100),
    container_number VARCHAR(100),
    storage_location VARCHAR(200),
    generation_date DATE NOT NULL,
    generator_id BIGINT NOT NULL,
    status ENUM('generated', 'stored', 'transferred', 'disposed') DEFAULT 'generated',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (source_chemical_id) REFERENCES chemicals(id),
    FOREIGN KEY (generator_id) REFERENCES users(id)
);
```

### 5. 培训管理相关表

#### training_courses (培训课程表)
```sql
CREATE TABLE training_courses (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    course_name VARCHAR(200) NOT NULL,
    course_type ENUM('safety', 'operation', 'emergency', 'regulation'),
    description TEXT,
    duration_hours DECIMAL(4,2),
    validity_months INT DEFAULT 12,
    course_content TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### user_training_records (用户培训记录表)
```sql
CREATE TABLE user_training_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    training_date DATE NOT NULL,
    score DECIMAL(5,2),
    status ENUM('passed', 'failed', 'pending'),
    certificate_number VARCHAR(100),
    expiry_date DATE,
    trainer VARCHAR(100),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (course_id) REFERENCES training_courses(id)
);
```

## 索引设计

```sql
-- 用户表索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_department ON users(department);

-- 化学品表索引
CREATE INDEX idx_chemicals_cas ON chemicals(cas_number);
CREATE INDEX idx_chemicals_name ON chemicals(chinese_name);

-- 库存表索引
CREATE INDEX idx_inventory_chemical ON inventory(chemical_id);
CREATE INDEX idx_inventory_location ON inventory(location_code);
CREATE INDEX idx_inventory_status ON inventory(status);
CREATE INDEX idx_inventory_expiry ON inventory(expiry_date);

-- 库存变动记录索引
CREATE INDEX idx_transactions_inventory ON inventory_transactions(inventory_id);
CREATE INDEX idx_transactions_date ON inventory_transactions(transaction_date);
CREATE INDEX idx_transactions_operator ON inventory_transactions(operator_id);
```

## 数据字典

详细的字段说明和约束条件请参考各表的CREATE语句注释。

## 备份策略

- 每日全量备份
- 每小时增量备份
- 保留30天备份数据
- 异地备份存储
