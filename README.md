# 实验室危险化学品智能安全管理系统 (LabChemSafe)

## 项目简介

本系统旨在为实验室提供一套全面、智能化的危险化学品全生命周期管理解决方案，确保化学品从采购、入库、存储、领用、使用、废弃到最终处置的每一个环节都符合安全规范。

## 技术栈

### 后端
- **框架**: Gin (Go)
- **数据库**: MySQL
- **认证**: JWT
- **文档**: Swagger

### 前端
- **框架**: React 18
- **构建工具**: Vite
- **UI库**: Ant Design
- **状态管理**: Redux Toolkit
- **路由**: React Router

## 项目结构

```
LabChemSafe/
├── backend/                 # 后端服务
│   ├── cmd/                # 应用入口
│   ├── internal/           # 内部包
│   │   ├── api/           # API处理器
│   │   ├── config/        # 配置
│   │   ├── middleware/    # 中间件
│   │   ├── model/         # 数据模型
│   │   ├── repository/    # 数据访问层
│   │   ├── service/       # 业务逻辑层
│   │   └── utils/         # 工具函数
│   ├── migrations/        # 数据库迁移
│   ├── docs/             # API文档
│   └── go.mod
├── frontend/              # 前端应用
│   ├── src/
│   │   ├── components/   # 通用组件
│   │   ├── pages/        # 页面组件
│   │   ├── services/     # API服务
│   │   ├── store/        # 状态管理
│   │   ├── utils/        # 工具函数
│   │   └── types/        # TypeScript类型定义
│   ├── public/
│   └── package.json
├── docs/                 # 项目文档
├── scripts/              # 部署脚本
└── docker-compose.yml    # Docker配置
```

## 主要功能模块

1. **危险化学品智能档案与信息库**
2. **库存精细化管理与全链条追溯**
3. **危险废物规范化管理与合规申报**
4. **安全培训与资质认证管理**
5. **应急管理与事故响应**
6. **权限管理与数据分析**

## 快速开始

### 环境要求
- Go 1.19+
- Node.js 16+
- MySQL 8.0+

### 后端启动
```bash
cd backend
go mod tidy
go run cmd/main.go
```

### 前端启动
```bash
cd frontend
npm install
npm run dev
```

## 开发规范

- 遵循Go和React最佳实践
- 使用Git Flow工作流
- 代码提交前必须通过测试
- API接口遵循RESTful设计原则

## 许可证

MIT License
