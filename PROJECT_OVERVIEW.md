# 儿童预防保健信息追溯管理系统

## 项目概述

本系统是一个基于区块链技术的儿童预防保健信息追溯管理平台，旨在建立可信、透明、可追溯的儿童健康档案管理体系。系统采用前后端分离架构，结合区块链技术确保数据的不可篡改性和可追溯性。

## 技术架构

### 前端技术栈
- **框架**: React 19.1.0 + TypeScript
- **构建工具**: Vite 7.0.4
- **UI组件库**: Ant Design 5.22.6
- **路由管理**: React Router DOM 6.28.1
- **状态管理**: Zustand 5.0.2
- **HTTP客户端**: Axios 1.7.9
- **图表组件**: Recharts 2.13.3
- **二维码**: qrcode 1.5.4
- **日期处理**: Day.js 1.11.13

### 后端技术栈
- **语言**: Go 1.24.4
- **Web框架**: Gin 1.10.1
- **数据库**: MySQL (GORM ORM)
- **认证**: JWT (golang-jwt/jwt/v5)
- **区块链**: Ethereum (go-ethereum 1.14.12)
- **日志**: Logrus 1.9.3
- **加密**: golang.org/x/crypto

## 核心功能模块

### 1. 用户认证与权限管理
- **用户注册/登录**: 支持用户名密码登录，JWT Token认证
- **角色权限**: 基于角色的访问控制(RBAC)
- **用户档案**: 个人信息管理，密码修改

### 2. 儿童档案管理
- **档案建立**: 儿童基本信息录入
- **监护人管理**: 监护人信息关联
- **档案查询**: 多条件检索儿童档案
- **档案更新**: 信息维护与更新

### 3. 疫苗追溯管理
- **疫苗档案**: 疫苗基础信息管理
- **批次管理**: 疫苗批次信息追溯
- **接种记录**: 疫苗接种全程记录
- **温度监控**: 疫苗存储温度异常预警

### 4. 预防保健服务记录
- **服务记录**: 体检、接种等服务记录
- **数字签名**: 医护人员和监护人双重确认
- **区块链上链**: 关键记录写入区块链确保不可篡改
- **记录查询**: 服务历史查询与统计

### 5. 家长端授权查询
- **移动端支持**: 生成授权二维码
- **智能提醒**: 疫苗接种、体检提醒
- **数字签名**: 家长数字签名确认
- **查询授权**: 安全的信息查询机制

### 6. 风险预警系统
- **实时监控**: 疫苗异常、过期等风险监控
- **预警通知**: 多渠道预警通知(App推送、短信、邮件)
- **事件处理**: 预警事件处理流程
- **统计分析**: 风险事件统计与分析

### 7. 跨区域数据协同
- **机构接入**: 多机构数据协同
- **数据同步**: 跨区域数据同步机制
- **监管审计**: 数据访问审计日志
- **统一标准**: 数据标准化管理

### 8. 工作台与统计
- **数据大屏**: 关键指标实时展示
- **待办事项**: 工作任务管理
- **统计报表**: 各类统计报表生成
- **系统监控**: 系统运行状态监控

## 数据库设计

### 核心数据表
- **users**: 用户基础信息
- **roles**: 角色权限管理
- **children**: 儿童档案信息
- **guardians**: 监护人信息
- **vaccines**: 疫苗基础信息
- **vaccine_batches**: 疫苗批次信息
- **vaccination_records**: 接种记录
- **health_checkups**: 体检记录
- **vaccine_traces**: 疫苗追溯记录

## API接口设计

### 认证模块
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `GET /api/v1/auth/profile` - 获取用户信息

### 儿童管理模块
- `GET /api/v1/children` - 获取儿童列表
- `POST /api/v1/children` - 创建儿童档案
- `PUT /api/v1/children/{id}` - 更新儿童信息
- `DELETE /api/v1/children/{id}` - 删除儿童档案

### 疫苗管理模块
- `GET /api/v1/vaccines` - 获取疫苗列表
- `POST /api/v1/vaccines` - 创建疫苗信息
- `GET /api/v1/vaccine-batches` - 获取疫苗批次
- `POST /api/v1/vaccination-records` - 创建接种记录

## 区块链集成

### 智能合约功能
- **数据上链**: 关键医疗记录写入区块链
- **数字签名**: 多方数字签名验证
- **数据验证**: 区块链数据完整性验证
- **追溯查询**: 基于区块链的数据追溯

### 上链数据类型
- 疫苗接种记录
- 健康体检记录
- 疫苗批次信息
- 数字签名信息

## 安全特性

### 数据安全
- **加密存储**: 敏感数据加密存储
- **传输加密**: HTTPS传输加密
- **访问控制**: 基于角色的访问控制
- **审计日志**: 完整的操作审计日志

### 区块链安全
- **不可篡改**: 区块链确保数据不可篡改
- **多重签名**: 关键操作需要多方确认
- **智能合约**: 自动化执行业务逻辑
- **去中心化**: 分布式数据存储

## 部署说明

### 环境要求
- Node.js 18+
- Go 1.24+
- MySQL 8.0+
- Ethereum节点

### 前端部署
```bash
cd frontend
npm install
npm run build
npm run preview
```

### 后端部署
```bash
cd backend
go mod tidy
go build -o main cmd/main.go
./main
```

## 项目特色

1. **区块链技术**: 确保医疗数据的可信性和不可篡改性
2. **多方协同**: 医护人员、监护人、监管部门多方参与
3. **智能提醒**: 基于时间和规则的智能提醒系统
4. **移动端支持**: 二维码授权，移动端友好
5. **跨区域协同**: 支持多机构、跨区域数据协同
6. **实时监控**: 疫苗温度、有效期等实时监控预警

## 未来规划

- [ ] 人工智能辅助诊断
- [ ] 更多区块链网络支持
- [ ] 国际化多语言支持
- [ ] 移动端原生应用
- [ ] 大数据分析平台
- [ ] 物联网设备集成

---

*本系统致力于构建安全、可信、高效的儿童预防保健信息管理平台，为儿童健康成长保驾护航。*