package main

import (
	"fmt"
	"log"
	"net/http"

	"labchemsafe/internal/api"
	"labchemsafe/internal/config"
	"labchemsafe/internal/middleware"
	"labchemsafe/internal/model"
	"labchemsafe/internal/utils"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	cfg := config.Load()

	// 初始化数据库
	if err := config.InitDatabase(&cfg.Database); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// 自动迁移数据库表
	if err := autoMigrate(); err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	// 初始化JWT
	utils.InitJWT(cfg.JWT.Secret)

	// 初始化默认数据
	if err := initDefaultData(); err != nil {
		log.Printf("Warning: Failed to initialize default data: %v", err)
	}

	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 创建路由
	router := setupRouter()

	// 启动服务器
	addr := fmt.Sprintf(":%s", cfg.Server.Port)
	log.Printf("Server starting on %s", addr)

	server := &http.Server{
		Addr:         addr,
		Handler:      router,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
	}

	if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		log.Fatalf("Failed to start server: %v", err)
	}
}

func setupRouter() *gin.Engine {
	router := gin.New()

	// 添加中间件
	router.Use(middleware.CORSMiddleware())
	router.Use(middleware.LoggerMiddleware())
	router.Use(gin.Recovery())

	// API路由组
	v1 := router.Group("/api/v1")

	// 认证相关路由
	authHandler := api.NewAuthHandler()
	auth := v1.Group("/auth")
	{
		auth.POST("/register", authHandler.Register)
		auth.POST("/login", authHandler.Login)
		auth.GET("/profile", middleware.AuthMiddleware(), authHandler.GetProfile)
		auth.PUT("/profile", middleware.AuthMiddleware(), authHandler.UpdateProfile)
		auth.POST("/change-password", middleware.AuthMiddleware(), authHandler.ChangePassword)
	}

	// 用户管理路由（需要管理员权限）
	users := v1.Group("/users")
	users.Use(middleware.AuthMiddleware())
	users.Use(middleware.RequireRole("admin"))
	{
		users.GET("", authHandler.GetUserList)
	}

	// 儿童档案管理路由
	childHandler := api.NewChildHandler()
	children := v1.Group("/children")
	children.Use(middleware.AuthMiddleware())
	{
		children.POST("", childHandler.CreateChild)                         // 创建儿童档案
		children.GET("", childHandler.GetChildList)                         // 获取儿童列表
		children.GET("/statistics", childHandler.GetChildStatistics)        // 获取统计信息
		children.GET("/search", childHandler.SearchChildren)                // 搜索儿童
		children.GET("/by-guardian", childHandler.GetChildrenByGuardian)    // 根据监护人获取儿童
		children.GET("/:id", childHandler.GetChild)                         // 获取儿童详情
		children.PUT("/:id", childHandler.UpdateChild)                      // 更新儿童档案
		children.DELETE("/:id", childHandler.DeleteChild)                   // 删除儿童档案
		children.GET("/:id/qrcode", childHandler.GenerateQRCode)            // 生成二维码
		children.GET("/child-id/:child_id", childHandler.GetChildByChildID) // 根据儿童ID获取档案
		children.GET("/validate/:child_id", childHandler.ValidateChildID)   // 验证儿童ID
	}

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, model.SuccessResponse(gin.H{
			"status":  "ok",
			"service": "labchemsafe",
		}))
	})

	return router
}

func autoMigrate() error {
	db := config.GetDB()
	return db.AutoMigrate(
		// 用户相关
		&model.User{},
		&model.Role{},
		&model.UserRole{},

		// 儿童档案相关
		&model.Child{},
		&model.Guardian{},

		// 疫苗相关
		&model.Vaccine{},
		&model.VaccineBatch{},
		&model.VaccineTrace{},
		&model.VaccinationRecord{},
		&model.HealthCheckup{},

		// 风险预警相关
		&model.RiskEvent{},
		&model.AdverseReaction{},
	)
}

func initDefaultData() error {
	db := config.GetDB()

	// 创建默认角色
	roles := []model.Role{
		{
			Name:        "admin",
			Description: "系统管理员",
		},
		{
			Name:        "manager",
			Description: "实验室管理员",
		},
		{
			Name:        "user",
			Description: "普通用户",
		},
	}

	for _, role := range roles {
		var existingRole model.Role
		if err := db.Where("name = ?", role.Name).First(&existingRole).Error; err != nil {
			if err := db.Create(&role).Error; err != nil {
				return fmt.Errorf("failed to create role %s: %w", role.Name, err)
			}
			log.Printf("Created default role: %s", role.Name)
		}
	}

	// 创建默认管理员用户
	var adminUser model.User
	if err := db.Where("username = ?", "admin").First(&adminUser).Error; err != nil {
		adminUser = model.User{
			Username:   "admin",
			Email:      "<EMAIL>",
			RealName:   "系统管理员",
			Department: "IT部门",
			Position:   "系统管理员",
			Status:     1,
		}

		if err := adminUser.SetPassword("admin123"); err != nil {
			return fmt.Errorf("failed to set admin password: %w", err)
		}

		if err := db.Create(&adminUser).Error; err != nil {
			return fmt.Errorf("failed to create admin user: %w", err)
		}

		// 分配管理员角色
		var adminRole model.Role
		if err := db.Where("name = ?", "admin").First(&adminRole).Error; err == nil {
			db.Model(&adminUser).Association("Roles").Append(&adminRole)
		}

		log.Println("Created default admin user: admin/admin123")
	}

	return nil
}
