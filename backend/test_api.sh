#!/bin/bash

# 儿童预防保健信息追溯管理系统 API 测试脚本

BASE_URL="http://localhost:8080/api/v1"

echo "=== 儿童预防保健信息追溯管理系统 API 测试 ==="
echo

# 1. 测试登录
echo "1. 测试管理员登录..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}')

echo "登录响应: $LOGIN_RESPONSE"

# 提取token
TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
echo "Token: $TOKEN"
echo

# 2. 测试获取儿童列表（空列表）
echo "2. 测试获取儿童档案列表..."
curl -s -X GET "$BASE_URL/children" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq '.'
echo

# 3. 测试创建儿童档案
echo "3. 测试创建儿童档案..."
CREATE_CHILD_RESPONSE=$(curl -s -X POST "$BASE_URL/children" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "张小明",
    "gender": "男",
    "birth_date": "2023-03-15T00:00:00Z",
    "birth_weight": 3.2,
    "birth_height": 50.0,
    "blood_type": "O",
    "guardian_name": "张华",
    "guardian_phone": "13800138001",
    "guardian_id_card": "110101199001011234",
    "guardian_relation": "父亲",
    "province": "北京市",
    "city": "北京市",
    "district": "朝阳区",
    "address": "朝阳区xxx街道xxx号",
    "allergies": "青霉素",
    "medical_history": "无",
    "family_history": "无"
  }')

echo "创建儿童档案响应: $CREATE_CHILD_RESPONSE"
echo

# 4. 再次获取儿童列表
echo "4. 再次获取儿童档案列表..."
curl -s -X GET "$BASE_URL/children" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq '.'
echo

# 5. 测试获取统计信息
echo "5. 测试获取统计信息..."
curl -s -X GET "$BASE_URL/children/statistics" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq '.'
echo

# 6. 测试健康检查接口
echo "6. 测试系统健康检查..."
curl -s -X GET "http://localhost:8080/health" | jq '.'
echo

echo "=== API 测试完成 ==="
