package api

import (
	"net/http"
	"strconv"

	"labchemsafe/internal/middleware"
	"labchemsafe/internal/model"
	"labchemsafe/internal/service"

	"github.com/gin-gonic/gin"
)

type ChildHandler struct {
	childService *service.ChildService
}

func NewChildHandler() *ChildHandler {
	return &ChildHandler{
		childService: service.NewChildService(),
	}
}

// CreateChild 创建儿童档案
func (h *ChildHandler) CreateChild(c *gin.Context) {
	var req model.ChildRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, err.Error()))
		return
	}

	// 验证数据
	if err := h.childService.ValidateChildData(&req); err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, err.Error()))
		return
	}

	// 获取当前用户ID
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		c.JSON(http.StatusUnauthorized, model.ErrorResponse(401, "用户未认证"))
		return
	}

	child, err := h.childService.CreateChild(&req, userID)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, err.Error()))
		return
	}

	c.JSON(http.StatusCreated, model.SuccessResponse(child.ToChildInfo()))
}

// GetChildList 获取儿童档案列表
func (h *ChildHandler) GetChildList(c *gin.Context) {
	// 分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))
	keyword := c.Query("keyword")
	gender := c.Query("gender")
	status := c.Query("status")

	if page < 1 {
		page = 1
	}
	if size < 1 || size > 100 {
		size = 10
	}

	result, err := h.childService.GetChildList(page, size, keyword, gender, status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, model.ErrorResponse(500, err.Error()))
		return
	}

	c.JSON(http.StatusOK, model.SuccessResponse(result))
}

// GetChild 获取儿童档案详情
func (h *ChildHandler) GetChild(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, "无效的儿童ID"))
		return
	}

	child, err := h.childService.GetChildByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, model.ErrorResponse(404, err.Error()))
		return
	}

	c.JSON(http.StatusOK, model.SuccessResponse(child))
}

// GetChildByChildID 根据儿童ID获取档案
func (h *ChildHandler) GetChildByChildID(c *gin.Context) {
	childID := c.Param("child_id")
	if childID == "" {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, "儿童ID不能为空"))
		return
	}

	child, err := h.childService.GetChildByChildID(childID)
	if err != nil {
		c.JSON(http.StatusNotFound, model.ErrorResponse(404, err.Error()))
		return
	}

	c.JSON(http.StatusOK, model.SuccessResponse(child))
}

// UpdateChild 更新儿童档案
func (h *ChildHandler) UpdateChild(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, "无效的儿童ID"))
		return
	}

	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, err.Error()))
		return
	}

	// 获取当前用户ID
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		c.JSON(http.StatusUnauthorized, model.ErrorResponse(401, "用户未认证"))
		return
	}

	if err := h.childService.UpdateChild(uint(id), updates, userID); err != nil {
		c.JSON(http.StatusInternalServerError, model.ErrorResponse(500, err.Error()))
		return
	}

	c.JSON(http.StatusOK, model.SuccessResponse(nil))
}

// DeleteChild 删除儿童档案
func (h *ChildHandler) DeleteChild(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, "无效的儿童ID"))
		return
	}

	// 获取当前用户ID
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		c.JSON(http.StatusUnauthorized, model.ErrorResponse(401, "用户未认证"))
		return
	}

	if err := h.childService.DeleteChild(uint(id), userID); err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, err.Error()))
		return
	}

	c.JSON(http.StatusOK, model.SuccessResponse(nil))
}

// GetChildStatistics 获取儿童档案统计信息
func (h *ChildHandler) GetChildStatistics(c *gin.Context) {
	stats, err := h.childService.GetChildStatistics()
	if err != nil {
		c.JSON(http.StatusInternalServerError, model.ErrorResponse(500, err.Error()))
		return
	}

	c.JSON(http.StatusOK, model.SuccessResponse(stats))
}

// GenerateQRCode 生成儿童健康身份二维码
func (h *ChildHandler) GenerateQRCode(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, "无效的儿童ID"))
		return
	}

	child, err := h.childService.GetChildByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, model.ErrorResponse(404, err.Error()))
		return
	}

	// 生成二维码数据
	qrData := map[string]interface{}{
		"child_id":           child.ChildID,
		"name":               child.Name,
		"blockchain_address": child.BlockchainAddress,
		"public_key":         child.PublicKey,
		"generated_at":       model.GetCurrentTime(),
	}

	c.JSON(http.StatusOK, model.SuccessResponse(qrData))
}

// SearchChildren 搜索儿童档案
func (h *ChildHandler) SearchChildren(c *gin.Context) {
	keyword := c.Query("q")
	if keyword == "" {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, "搜索关键词不能为空"))
		return
	}

	result, err := h.childService.GetChildList(1, 20, keyword, "", "")
	if err != nil {
		c.JSON(http.StatusInternalServerError, model.ErrorResponse(500, err.Error()))
		return
	}

	c.JSON(http.StatusOK, model.SuccessResponse(result))
}

// GetChildrenByGuardian 根据监护人获取儿童列表（家长端使用）
func (h *ChildHandler) GetChildrenByGuardian(c *gin.Context) {
	phone := c.Query("phone")
	if phone == "" {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, "监护人电话不能为空"))
		return
	}

	children, err := h.childService.GetChildrenByGuardianPhone(phone)
	if err != nil {
		c.JSON(http.StatusInternalServerError, model.ErrorResponse(500, err.Error()))
		return
	}

	// 转换为响应格式
	childInfos := make([]*model.ChildInfo, len(children))
	for i, child := range children {
		childInfos[i] = child.ToChildInfo()
	}

	c.JSON(http.StatusOK, model.SuccessResponse(childInfos))
}

// ValidateChildID 验证儿童ID是否存在
func (h *ChildHandler) ValidateChildID(c *gin.Context) {
	childID := c.Param("child_id")
	if childID == "" {
		c.JSON(http.StatusBadRequest, model.ErrorResponse(400, "儿童ID不能为空"))
		return
	}

	child, err := h.childService.GetChildByChildID(childID)
	if err != nil {
		c.JSON(http.StatusNotFound, model.ErrorResponse(404, "儿童档案不存在"))
		return
	}

	// 只返回基本信息用于验证
	basicInfo := map[string]interface{}{
		"child_id": child.ChildID,
		"name":     child.Name,
		"gender":   child.Gender,
		"age":      int(model.GetAge(child.BirthDate)),
		"status":   child.Status,
	}

	c.JSON(http.StatusOK, model.SuccessResponse(basicInfo))
}
