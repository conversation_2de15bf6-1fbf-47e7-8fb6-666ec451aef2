package config

import (
	"fmt"
	"log"
	"os"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var DB *gorm.DB

func InitDatabase(config *DatabaseConfig) error {
	var err error

	// 检查是否使用SQLite（开发环境）
	if os.Getenv("DB_TYPE") == "sqlite" || config.Host == "" {
		// 使用SQLite数据库
		dbPath := "child_health_tracker.db"
		if config.DBName != "" {
			dbPath = config.DBName + ".db"
		}

		DB, err = gorm.Open(sqlite.Open(dbPath), &gorm.Config{
			Logger: logger.Default.LogMode(logger.Info),
		})

		if err != nil {
			return fmt.Errorf("failed to connect to SQLite database: %w", err)
		}

		log.Println("Connected to SQLite database:", dbPath)
	} else {
		// 使用MySQL数据库
		dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=True&loc=Local",
			config.User,
			config.Password,
			config.Host,
			config.Port,
			config.DBName,
			config.Charset,
		)

		DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
			Logger: logger.Default.LogMode(logger.Info),
		})

		if err != nil {
			return fmt.Errorf("failed to connect to MySQL database: %w", err)
		}

		log.Println("Connected to MySQL database:", config.DBName)
	}

	// 获取底层的sql.DB对象来配置连接池
	sqlDB, err := DB.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(10)           // 最大空闲连接数
	sqlDB.SetMaxOpenConns(100)          // 最大打开连接数
	sqlDB.SetConnMaxLifetime(time.Hour) // 连接最大生存时间

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %w", err)
	}

	log.Println("Database connected successfully")
	return nil
}

func GetDB() *gorm.DB {
	return DB
}
