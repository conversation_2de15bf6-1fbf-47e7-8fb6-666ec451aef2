package model

import (
	"time"
)

// Vaccine 疫苗基础信息模型
type Vaccine struct {
	BaseModel
	Name            string `json:"name" gorm:"size:200;not null;comment:疫苗名称"`
	Type            string `json:"type" gorm:"size:100;not null;comment:疫苗类型"`
	Manufacturer    string `json:"manufacturer" gorm:"size:200;not null;comment:生产厂家"`
	Specification   string `json:"specification" gorm:"size:100;comment:规格"`
	StorageTemp     string `json:"storage_temp" gorm:"size:50;comment:储存温度要求"`
	ValidityPeriod  int    `json:"validity_period" gorm:"comment:有效期(月)"`
	Description     string `json:"description" gorm:"type:text;comment:疫苗描述"`
	Status          int    `json:"status" gorm:"default:1;comment:1:正常 0:停用"`
}

// VaccineBatch 疫苗批次模型
type VaccineBatch struct {
	BaseModel
	BatchNumber     string    `json:"batch_number" gorm:"uniqueIndex;size:100;not null;comment:批次号"`
	VaccineID       uint      `json:"vaccine_id" gorm:"not null;comment:疫苗ID"`
	ProductionDate  time.Time `json:"production_date" gorm:"not null;comment:生产日期"`
	ExpiryDate      time.Time `json:"expiry_date" gorm:"not null;comment:过期日期"`
	Quantity        int       `json:"quantity" gorm:"not null;comment:生产数量"`
	RemainingQty    int       `json:"remaining_qty" gorm:"not null;comment:剩余数量"`
	QualityStatus   string    `json:"quality_status" gorm:"size:50;default:'qualified';comment:质量状态"`
	
	// 区块链追溯信息
	BlockchainTxHash string `json:"blockchain_tx_hash" gorm:"size:100;comment:上链交易哈希"`
	TraceCode       string `json:"trace_code" gorm:"uniqueIndex;size:100;comment:追溯码"`
	
	// 关联关系
	Vaccine         Vaccine           `json:"vaccine" gorm:"foreignKey:VaccineID"`
	TraceRecords    []VaccineTrace    `json:"trace_records,omitempty" gorm:"foreignKey:BatchID"`
	VaccinationRecords []VaccinationRecord `json:"vaccination_records,omitempty" gorm:"foreignKey:BatchID"`
}

// VaccineTrace 疫苗追溯记录模型
type VaccineTrace struct {
	BaseModel
	BatchID         uint      `json:"batch_id" gorm:"not null;comment:批次ID"`
	Step            int       `json:"step" gorm:"not null;comment:追溯步骤"`
	Location        string    `json:"location" gorm:"size:200;not null;comment:地点"`
	Operator        string    `json:"operator" gorm:"size:100;not null;comment:操作人"`
	OperatorID      uint      `json:"operator_id" gorm:"comment:操作人ID"`
	Operation       string    `json:"operation" gorm:"size:100;not null;comment:操作类型"`
	Temperature     float64   `json:"temperature" gorm:"comment:温度"`
	Humidity        float64   `json:"humidity" gorm:"comment:湿度"`
	Description     string    `json:"description" gorm:"type:text;comment:描述"`
	Timestamp       time.Time `json:"timestamp" gorm:"not null;comment:操作时间"`
	BlockchainTxHash string   `json:"blockchain_tx_hash" gorm:"size:100;comment:区块链交易哈希"`
	
	// 关联关系
	Batch           VaccineBatch `json:"batch" gorm:"foreignKey:BatchID"`
}

// VaccinationRecord 接种记录模型
type VaccinationRecord struct {
	BaseModel
	RecordID        string    `json:"record_id" gorm:"uniqueIndex;size:50;not null;comment:记录ID"`
	ChildID         uint      `json:"child_id" gorm:"not null;comment:儿童ID"`
	VaccineID       uint      `json:"vaccine_id" gorm:"not null;comment:疫苗ID"`
	BatchID         uint      `json:"batch_id" gorm:"not null;comment:批次ID"`
	DoseNumber      int       `json:"dose_number" gorm:"not null;comment:第几剂"`
	VaccinationDate time.Time `json:"vaccination_date" gorm:"not null;comment:接种日期"`
	
	// 接种信息
	Location        string `json:"location" gorm:"size:200;not null;comment:接种地点"`
	Operator        string `json:"operator" gorm:"size:100;not null;comment:接种人员"`
	OperatorID      uint   `json:"operator_id" gorm:"comment:接种人员ID"`
	InjectionSite   string `json:"injection_site" gorm:"size:50;comment:接种部位"`
	
	// 确认状态
	OperatorSigned  bool      `json:"operator_signed" gorm:"default:false;comment:医护人员签名"`
	GuardianSigned  bool      `json:"guardian_signed" gorm:"default:false;comment:监护人签名"`
	Status          string    `json:"status" gorm:"size:50;default:'pending';comment:状态"`
	ConfirmedAt     *time.Time `json:"confirmed_at" gorm:"comment:确认时间"`
	
	// 区块链信息
	BlockchainTxHash string `json:"blockchain_tx_hash" gorm:"size:100;comment:区块链交易哈希"`
	
	// 不良反应
	AdverseReaction string `json:"adverse_reaction" gorm:"type:text;comment:不良反应"`
	
	// 关联关系
	Child           Child        `json:"child" gorm:"foreignKey:ChildID"`
	Vaccine         Vaccine      `json:"vaccine" gorm:"foreignKey:VaccineID"`
	Batch           VaccineBatch `json:"batch" gorm:"foreignKey:BatchID"`
}

// HealthCheckup 体检记录模型
type HealthCheckup struct {
	BaseModel
	RecordID        string    `json:"record_id" gorm:"uniqueIndex;size:50;not null;comment:记录ID"`
	ChildID         uint      `json:"child_id" gorm:"not null;comment:儿童ID"`
	CheckupDate     time.Time `json:"checkup_date" gorm:"not null;comment:体检日期"`
	CheckupType     string    `json:"checkup_type" gorm:"size:100;not null;comment:体检类型"`
	
	// 体检信息
	Height          float64 `json:"height" gorm:"comment:身高(cm)"`
	Weight          float64 `json:"weight" gorm:"comment:体重(kg)"`
	HeadCircumference float64 `json:"head_circumference" gorm:"comment:头围(cm)"`
	
	// 体检结果
	PhysicalDevelopment string `json:"physical_development" gorm:"size:100;comment:体格发育"`
	MentalDevelopment   string `json:"mental_development" gorm:"size:100;comment:智力发育"`
	VisionScreening     string `json:"vision_screening" gorm:"size:100;comment:视力筛查"`
	HearingScreening    string `json:"hearing_screening" gorm:"size:100;comment:听力筛查"`
	
	// 医生信息
	Doctor          string `json:"doctor" gorm:"size:100;not null;comment:体检医生"`
	DoctorID        uint   `json:"doctor_id" gorm:"comment:医生ID"`
	Location        string `json:"location" gorm:"size:200;not null;comment:体检地点"`
	
	// 确认状态
	DoctorSigned    bool      `json:"doctor_signed" gorm:"default:false;comment:医生签名"`
	GuardianSigned  bool      `json:"guardian_signed" gorm:"default:false;comment:监护人签名"`
	Status          string    `json:"status" gorm:"size:50;default:'pending';comment:状态"`
	ConfirmedAt     *time.Time `json:"confirmed_at" gorm:"comment:确认时间"`
	
	// 区块链信息
	BlockchainTxHash string `json:"blockchain_tx_hash" gorm:"size:100;comment:区块链交易哈希"`
	
	// 备注
	Notes           string `json:"notes" gorm:"type:text;comment:备注"`
	Recommendations string `json:"recommendations" gorm:"type:text;comment:建议"`
	
	// 关联关系
	Child           Child `json:"child" gorm:"foreignKey:ChildID"`
}

// VaccineTraceRequest 疫苗追溯请求
type VaccineTraceRequest struct {
	BatchNumber string `json:"batch_number" form:"batch_number"`
	TraceCode   string `json:"trace_code" form:"trace_code"`
}

// VaccineTraceResponse 疫苗追溯响应
type VaccineTraceResponse struct {
	Batch        VaccineBatch   `json:"batch"`
	TraceRecords []VaccineTrace `json:"trace_records"`
	IsValid      bool           `json:"is_valid"`
	Temperature  float64        `json:"current_temperature"`
	Status       string         `json:"status"`
}

// VaccinationRequest 接种记录请求
type VaccinationRequest struct {
	ChildID         uint      `json:"child_id" binding:"required"`
	VaccineID       uint      `json:"vaccine_id" binding:"required"`
	BatchNumber     string    `json:"batch_number" binding:"required"`
	DoseNumber      int       `json:"dose_number" binding:"required"`
	VaccinationDate time.Time `json:"vaccination_date" binding:"required"`
	Location        string    `json:"location" binding:"required"`
	InjectionSite   string    `json:"injection_site"`
}

// HealthCheckupRequest 体检记录请求
type HealthCheckupRequest struct {
	ChildID             uint      `json:"child_id" binding:"required"`
	CheckupDate         time.Time `json:"checkup_date" binding:"required"`
	CheckupType         string    `json:"checkup_type" binding:"required"`
	Height              float64   `json:"height"`
	Weight              float64   `json:"weight"`
	HeadCircumference   float64   `json:"head_circumference"`
	PhysicalDevelopment string    `json:"physical_development"`
	MentalDevelopment   string    `json:"mental_development"`
	VisionScreening     string    `json:"vision_screening"`
	HearingScreening    string    `json:"hearing_screening"`
	Location            string    `json:"location" binding:"required"`
	Notes               string    `json:"notes"`
	Recommendations     string    `json:"recommendations"`
}

// TableName 指定表名
func (Vaccine) TableName() string {
	return "vaccines"
}

func (VaccineBatch) TableName() string {
	return "vaccine_batches"
}

func (VaccineTrace) TableName() string {
	return "vaccine_traces"
}

func (VaccinationRecord) TableName() string {
	return "vaccination_records"
}

func (HealthCheckup) TableName() string {
	return "health_checkups"
}

// GenerateRecordID 生成记录ID
func GenerateVaccinationRecordID() string {
	now := time.Now()
	return "VR" + now.Format("20060102") + GenerateRandomString(6)
}

func GenerateHealthCheckupRecordID() string {
	now := time.Now()
	return "HC" + now.Format("20060102") + GenerateRandomString(6)
}

// IsExpired 检查疫苗是否过期
func (vb *VaccineBatch) IsExpired() bool {
	return time.Now().After(vb.ExpiryDate)
}

// IsTemperatureNormal 检查温度是否正常
func (vt *VaccineTrace) IsTemperatureNormal() bool {
	return vt.Temperature >= 2.0 && vt.Temperature <= 8.0
}
