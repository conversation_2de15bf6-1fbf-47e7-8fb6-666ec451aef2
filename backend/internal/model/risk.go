package model

import (
	"time"
)

// RiskEvent 风险事件模型
type RiskEvent struct {
	BaseModel
	EventID         string    `json:"event_id" gorm:"uniqueIndex;size:50;not null;comment:事件ID"`
	Type            string    `json:"type" gorm:"size:50;not null;comment:事件类型"`
	Title           string    `json:"title" gorm:"size:200;not null;comment:事件标题"`
	Description     string    `json:"description" gorm:"type:text;comment:事件描述"`
	Severity        string    `json:"severity" gorm:"size:20;not null;comment:严重程度"`
	Status          string    `json:"status" gorm:"size:20;default:'active';comment:状态"`
	
	// 关联信息
	BatchNumber     string `json:"batch_number" gorm:"size:100;comment:相关批次号"`
	VaccineID       uint   `json:"vaccine_id" gorm:"comment:相关疫苗ID"`
	Location        string `json:"location" gorm:"size:200;comment:发生地点"`
	
	// 影响范围
	AffectedCount   int    `json:"affected_count" gorm:"default:0;comment:影响人数"`
	AffectedRegion  string `json:"affected_region" gorm:"size:200;comment:影响区域"`
	
	// 处理信息
	ReportedBy      uint      `json:"reported_by" gorm:"comment:报告人ID"`
	ReportTime      time.Time `json:"report_time" gorm:"not null;comment:报告时间"`
	HandledBy       uint      `json:"handled_by" gorm:"comment:处理人ID"`
	HandledAt       *time.Time `json:"handled_at" gorm:"comment:处理时间"`
	Resolution      string    `json:"resolution" gorm:"type:text;comment:处理方案"`
	
	// 区块链信息
	BlockchainTxHash string `json:"blockchain_tx_hash" gorm:"size:100;comment:区块链交易哈希"`
	
	// 关联关系
	Notifications   []RiskNotification `json:"notifications,omitempty" gorm:"foreignKey:EventID"`
	AffectedChildren []AffectedChild   `json:"affected_children,omitempty" gorm:"foreignKey:EventID"`
}

// RiskNotification 风险通知模型
type RiskNotification struct {
	BaseModel
	EventID         uint      `json:"event_id" gorm:"not null;comment:事件ID"`
	NotificationType string   `json:"notification_type" gorm:"size:50;not null;comment:通知类型"`
	Recipient       string    `json:"recipient" gorm:"size:200;not null;comment:接收人"`
	RecipientType   string    `json:"recipient_type" gorm:"size:20;not null;comment:接收人类型"`
	Channel         string    `json:"channel" gorm:"size:20;not null;comment:通知渠道"`
	Content         string    `json:"content" gorm:"type:text;comment:通知内容"`
	Status          string    `json:"status" gorm:"size:20;default:'pending';comment:发送状态"`
	SentAt          *time.Time `json:"sent_at" gorm:"comment:发送时间"`
	ReadAt          *time.Time `json:"read_at" gorm:"comment:阅读时间"`
	
	// 关联关系
	Event           RiskEvent `json:"event" gorm:"foreignKey:EventID"`
}

// AffectedChild 受影响儿童模型
type AffectedChild struct {
	BaseModel
	EventID         uint   `json:"event_id" gorm:"not null;comment:事件ID"`
	ChildID         uint   `json:"child_id" gorm:"not null;comment:儿童ID"`
	VaccinationRecordID uint `json:"vaccination_record_id" gorm:"comment:相关接种记录ID"`
	RiskLevel       string `json:"risk_level" gorm:"size:20;comment:风险等级"`
	Status          string `json:"status" gorm:"size:20;default:'identified';comment:处理状态"`
	NotifiedAt      *time.Time `json:"notified_at" gorm:"comment:通知时间"`
	HandledAt       *time.Time `json:"handled_at" gorm:"comment:处理时间"`
	
	// 关联关系
	Event           RiskEvent         `json:"event" gorm:"foreignKey:EventID"`
	Child           Child             `json:"child" gorm:"foreignKey:ChildID"`
	VaccinationRecord *VaccinationRecord `json:"vaccination_record,omitempty" gorm:"foreignKey:VaccinationRecordID"`
}

// AdverseReaction 不良反应报告模型
type AdverseReaction struct {
	BaseModel
	ReportID        string    `json:"report_id" gorm:"uniqueIndex;size:50;not null;comment:报告ID"`
	ChildID         uint      `json:"child_id" gorm:"not null;comment:儿童ID"`
	VaccinationRecordID uint  `json:"vaccination_record_id" gorm:"not null;comment:接种记录ID"`
	
	// 反应信息
	ReactionType    string    `json:"reaction_type" gorm:"size:100;not null;comment:反应类型"`
	Severity        string    `json:"severity" gorm:"size:20;not null;comment:严重程度"`
	OnsetTime       time.Time `json:"onset_time" gorm:"not null;comment:发生时间"`
	Duration        int       `json:"duration" gorm:"comment:持续时间(小时)"`
	Symptoms        string    `json:"symptoms" gorm:"type:text;comment:症状描述"`
	Treatment       string    `json:"treatment" gorm:"type:text;comment:治疗措施"`
	Outcome         string    `json:"outcome" gorm:"size:50;comment:结局"`
	
	// 报告信息
	ReportedBy      uint      `json:"reported_by" gorm:"comment:报告人ID"`
	ReporterType    string    `json:"reporter_type" gorm:"size:20;comment:报告人类型"`
	ReportTime      time.Time `json:"report_time" gorm:"not null;comment:报告时间"`
	
	// 审核信息
	ReviewedBy      uint      `json:"reviewed_by" gorm:"comment:审核人ID"`
	ReviewedAt      *time.Time `json:"reviewed_at" gorm:"comment:审核时间"`
	ReviewStatus    string    `json:"review_status" gorm:"size:20;default:'pending';comment:审核状态"`
	ReviewComments  string    `json:"review_comments" gorm:"type:text;comment:审核意见"`
	
	// 区块链信息
	BlockchainTxHash string `json:"blockchain_tx_hash" gorm:"size:100;comment:区块链交易哈希"`
	
	// 关联关系
	Child           Child             `json:"child" gorm:"foreignKey:ChildID"`
	VaccinationRecord VaccinationRecord `json:"vaccination_record" gorm:"foreignKey:VaccinationRecordID"`
}

// TemperatureMonitor 温度监控模型
type TemperatureMonitor struct {
	BaseModel
	DeviceID        string    `json:"device_id" gorm:"size:50;not null;comment:设备ID"`
	Location        string    `json:"location" gorm:"size:200;not null;comment:监控地点"`
	Temperature     float64   `json:"temperature" gorm:"not null;comment:温度"`
	Humidity        float64   `json:"humidity" gorm:"comment:湿度"`
	RecordTime      time.Time `json:"record_time" gorm:"not null;comment:记录时间"`
	Status          string    `json:"status" gorm:"size:20;default:'normal';comment:状态"`
	AlertTriggered  bool      `json:"alert_triggered" gorm:"default:false;comment:是否触发预警"`
	
	// 关联批次
	BatchNumber     string `json:"batch_number" gorm:"size:100;comment:相关批次号"`
}

// 请求和响应模型

// RiskEventRequest 风险事件请求
type RiskEventRequest struct {
	Type            string `json:"type" binding:"required"`
	Title           string `json:"title" binding:"required"`
	Description     string `json:"description"`
	Severity        string `json:"severity" binding:"required,oneof=high medium low"`
	BatchNumber     string `json:"batch_number"`
	VaccineID       uint   `json:"vaccine_id"`
	Location        string `json:"location"`
	AffectedRegion  string `json:"affected_region"`
}

// NotificationRequest 通知请求
type NotificationRequest struct {
	EventID         uint     `json:"event_id" binding:"required"`
	NotificationType string  `json:"notification_type" binding:"required"`
	Channels        []string `json:"channels" binding:"required"`
	Urgency         string   `json:"urgency" binding:"required"`
	Content         string   `json:"content" binding:"required"`
}

// AdverseReactionRequest 不良反应报告请求
type AdverseReactionRequest struct {
	ChildID             uint      `json:"child_id" binding:"required"`
	VaccinationRecordID uint      `json:"vaccination_record_id" binding:"required"`
	ReactionType        string    `json:"reaction_type" binding:"required"`
	Severity            string    `json:"severity" binding:"required,oneof=mild moderate severe"`
	OnsetTime           time.Time `json:"onset_time" binding:"required"`
	Duration            int       `json:"duration"`
	Symptoms            string    `json:"symptoms" binding:"required"`
	Treatment           string    `json:"treatment"`
	Outcome             string    `json:"outcome"`
	ReporterType        string    `json:"reporter_type" binding:"required,oneof=guardian healthcare_worker"`
}

// RiskStatistics 风险统计
type RiskStatistics struct {
	ActiveEvents    int `json:"active_events"`
	ProcessingEvents int `json:"processing_events"`
	ResolvedEvents  int `json:"resolved_events"`
	TotalAffected   int `json:"total_affected"`
	HighRiskEvents  int `json:"high_risk_events"`
	AdverseReactions int `json:"adverse_reactions"`
}

// TableName 指定表名
func (RiskEvent) TableName() string {
	return "risk_events"
}

func (RiskNotification) TableName() string {
	return "risk_notifications"
}

func (AffectedChild) TableName() string {
	return "affected_children"
}

func (AdverseReaction) TableName() string {
	return "adverse_reactions"
}

func (TemperatureMonitor) TableName() string {
	return "temperature_monitors"
}

// GenerateEventID 生成事件ID
func GenerateRiskEventID() string {
	now := time.Now()
	return "RE" + now.Format("20060102") + GenerateRandomString(6)
}

func GenerateAdverseReactionID() string {
	now := time.Now()
	return "AR" + now.Format("20060102") + GenerateRandomString(6)
}

// IsHighRisk 判断是否为高风险事件
func (re *RiskEvent) IsHighRisk() bool {
	return re.Severity == "high"
}

// IsTemperatureAlert 判断温度是否异常
func (tm *TemperatureMonitor) IsTemperatureAlert() bool {
	return tm.Temperature < 2.0 || tm.Temperature > 8.0
}
