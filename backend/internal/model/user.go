package model

import (
	"encoding/json"
	"time"

	"golang.org/x/crypto/bcrypt"
)

// User 系统用户模型（医护人员、管理员等）
type User struct {
	BaseModel
	Username     string    `json:"username" gorm:"uniqueIndex;size:50;not null"`
	Email        string    `json:"email" gorm:"uniqueIndex;size:100;not null"`
	PasswordHash string    `json:"-" gorm:"size:255;not null"`
	RealName     string    `json:"real_name" gorm:"size:100;not null"`
	Phone        string    `json:"phone" gorm:"size:20"`
	Department   string    `json:"department" gorm:"size:100"`
	Position     string    `json:"position" gorm:"size:100"`
	Institution  string    `json:"institution" gorm:"size:200;comment:所属机构"`
	LicenseNo    string    `json:"license_no" gorm:"size:100;comment:执业证书号"`
	Status       int       `json:"status" gorm:"default:1;comment:1:正常 0:禁用"`
	LastLoginAt  time.Time `json:"last_login_at"`
	Roles        []Role    `json:"roles" gorm:"many2many:user_roles;"`
}

// Role 角色模型
type Role struct {
	BaseModel
	Name        string          `json:"name" gorm:"uniqueIndex;size:50;not null"`
	Description string          `json:"description" gorm:"type:text"`
	Permissions json.RawMessage `json:"permissions" gorm:"type:json"`
	Users       []User          `json:"-" gorm:"many2many:user_roles;"`
}

// UserRole 用户角色关联表
type UserRole struct {
	UserID uint `json:"user_id" gorm:"primaryKey"`
	RoleID uint `json:"role_id" gorm:"primaryKey"`
}

// RegisterRequest 注册请求
type RegisterRequest struct {
	Username   string `json:"username" binding:"required,min=3,max=50"`
	Email      string `json:"email" binding:"required,email"`
	Password   string `json:"password" binding:"required,min=6"`
	RealName   string `json:"real_name" binding:"required,max=100"`
	Phone      string `json:"phone" binding:"max=20"`
	Department string `json:"department" binding:"max=100"`
	Position   string `json:"position" binding:"max=100"`
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	Token string    `json:"token"`
	User  *UserInfo `json:"user"`
}

// UserInfo 用户信息
type UserInfo struct {
	ID         uint     `json:"id"`
	Username   string   `json:"username"`
	Email      string   `json:"email"`
	RealName   string   `json:"real_name"`
	Phone      string   `json:"phone"`
	Department string   `json:"department"`
	Position   string   `json:"position"`
	Status     int      `json:"status"`
	Roles      []string `json:"roles"`
}

// SetPassword 设置密码
func (u *User) SetPassword(password string) error {
	hash, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	u.PasswordHash = string(hash)
	return nil
}

// CheckPassword 验证密码
func (u *User) CheckPassword(password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(u.PasswordHash), []byte(password))
	return err == nil
}

// ToUserInfo 转换为用户信息
func (u *User) ToUserInfo() *UserInfo {
	roles := make([]string, len(u.Roles))
	for i, role := range u.Roles {
		roles[i] = role.Name
	}

	return &UserInfo{
		ID:         u.ID,
		Username:   u.Username,
		Email:      u.Email,
		RealName:   u.RealName,
		Phone:      u.Phone,
		Department: u.Department,
		Position:   u.Position,
		Status:     u.Status,
		Roles:      roles,
	}
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

func (Role) TableName() string {
	return "roles"
}

func (UserRole) TableName() string {
	return "user_roles"
}
