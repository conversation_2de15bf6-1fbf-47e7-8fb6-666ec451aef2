package model

import (
	"time"
)

// Child 儿童档案模型
type Child struct {
	BaseModel
	ChildID     string    `json:"child_id" gorm:"uniqueIndex;size:50;not null;comment:儿童唯一标识"`
	Name        string    `json:"name" gorm:"size:100;not null;comment:儿童姓名"`
	Gender      string    `json:"gender" gorm:"size:10;not null;comment:性别"`
	BirthDate   time.Time `json:"birth_date" gorm:"not null;comment:出生日期"`
	BirthWeight float64   `json:"birth_weight" gorm:"comment:出生体重(kg)"`
	BirthHeight float64   `json:"birth_height" gorm:"comment:出生身高(cm)"`
	BloodType   string    `json:"blood_type" gorm:"size:10;comment:血型"`

	// 监护人信息
	GuardianName     string `json:"guardian_name" gorm:"size:100;not null;comment:监护人姓名"`
	GuardianPhone    string `json:"guardian_phone" gorm:"size:20;not null;comment:监护人电话"`
	GuardianIDCard   string `json:"guardian_id_card" gorm:"size:20;comment:监护人身份证"`
	GuardianRelation string `json:"guardian_relation" gorm:"size:20;comment:与儿童关系"`

	// 地址信息
	Province string `json:"province" gorm:"size:50;comment:省份"`
	City     string `json:"city" gorm:"size:50;comment:城市"`
	District string `json:"district" gorm:"size:50;comment:区县"`
	Address  string `json:"address" gorm:"size:500;comment:详细地址"`

	// 健康信息
	Allergies      string `json:"allergies" gorm:"type:text;comment:过敏史"`
	MedicalHistory string `json:"medical_history" gorm:"type:text;comment:既往病史"`
	FamilyHistory  string `json:"family_history" gorm:"type:text;comment:家族病史"`

	// 区块链相关
	BlockchainAddress string `json:"blockchain_address" gorm:"size:100;comment:区块链地址"`
	PublicKey         string `json:"public_key" gorm:"type:text;comment:公钥"`
	CreationTxHash    string `json:"creation_tx_hash" gorm:"size:100;comment:创建交易哈希"`

	// 状态信息
	Status           int       `json:"status" gorm:"default:1;comment:1:正常 0:暂停 -1:异常"`
	RegistrationDate time.Time `json:"registration_date" gorm:"not null;comment:注册日期"`
	LastUpdateBy     uint      `json:"last_update_by" gorm:"comment:最后更新人"`

	// 关联关系
	VaccinationRecords []VaccinationRecord `json:"vaccination_records,omitempty" gorm:"foreignKey:ChildID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
	HealthCheckups     []HealthCheckup     `json:"health_checkups,omitempty" gorm:"foreignKey:ChildID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
}

// ChildRequest 儿童档案请求模型
type ChildRequest struct {
	Name             string    `json:"name" binding:"required,max=100"`
	Gender           string    `json:"gender" binding:"required,oneof=男 女"`
	BirthDate        time.Time `json:"birth_date" binding:"required"`
	BirthWeight      float64   `json:"birth_weight"`
	BirthHeight      float64   `json:"birth_height"`
	BloodType        string    `json:"blood_type"`
	GuardianName     string    `json:"guardian_name" binding:"required,max=100"`
	GuardianPhone    string    `json:"guardian_phone" binding:"required,len=11"`
	GuardianIDCard   string    `json:"guardian_id_card"`
	GuardianRelation string    `json:"guardian_relation"`
	Province         string    `json:"province"`
	City             string    `json:"city"`
	District         string    `json:"district"`
	Address          string    `json:"address"`
	Allergies        string    `json:"allergies"`
	MedicalHistory   string    `json:"medical_history"`
	FamilyHistory    string    `json:"family_history"`
}

// ChildInfo 儿童信息响应模型
type ChildInfo struct {
	ID               uint       `json:"id"`
	ChildID          string     `json:"child_id"`
	Name             string     `json:"name"`
	Gender           string     `json:"gender"`
	BirthDate        time.Time  `json:"birth_date"`
	Age              int        `json:"age"`
	GuardianName     string     `json:"guardian_name"`
	GuardianPhone    string     `json:"guardian_phone"`
	Address          string     `json:"address"`
	Status           int        `json:"status"`
	RegistrationDate time.Time  `json:"registration_date"`
	VaccinationCount int        `json:"vaccination_count"`
	LastVaccination  *time.Time `json:"last_vaccination,omitempty"`
}

// Guardian 监护人模型（用于家长端）
type Guardian struct {
	BaseModel
	Phone          string    `json:"phone" gorm:"uniqueIndex;size:20;not null;comment:手机号"`
	Name           string    `json:"name" gorm:"size:100;not null;comment:姓名"`
	IDCard         string    `json:"id_card" gorm:"size:20;comment:身份证号"`
	WechatOpenID   string    `json:"wechat_open_id" gorm:"size:100;comment:微信OpenID"`
	PrivateKeyHash string    `json:"private_key_hash" gorm:"size:255;comment:私钥哈希"`
	Status         int       `json:"status" gorm:"default:1;comment:1:正常 0:禁用"`
	LastLoginAt    time.Time `json:"last_login_at"`

	// 关联的儿童
	Children []Child `json:"children,omitempty" gorm:"foreignKey:GuardianPhone;references:Phone"`
}

// GuardianLoginRequest 监护人登录请求
type GuardianLoginRequest struct {
	Phone  string `json:"phone" binding:"required,len=11"`
	Code   string `json:"code" binding:"required,len=6"`
	OpenID string `json:"open_id"`
}

// GuardianAuthResponse 监护人认证响应
type GuardianAuthResponse struct {
	Token    string      `json:"token"`
	Guardian *Guardian   `json:"guardian"`
	Children []ChildInfo `json:"children"`
}

// TableName 指定表名
func (Child) TableName() string {
	return "children"
}

func (Guardian) TableName() string {
	return "guardians"
}

// ToChildInfo 转换为儿童信息响应
func (c *Child) ToChildInfo() *ChildInfo {
	age := int(time.Since(c.BirthDate).Hours() / 24 / 365)

	info := &ChildInfo{
		ID:               c.ID,
		ChildID:          c.ChildID,
		Name:             c.Name,
		Gender:           c.Gender,
		BirthDate:        c.BirthDate,
		Age:              age,
		GuardianName:     c.GuardianName,
		GuardianPhone:    c.GuardianPhone,
		Address:          c.GetFullAddress(),
		Status:           c.Status,
		RegistrationDate: c.RegistrationDate,
		VaccinationCount: len(c.VaccinationRecords),
	}

	// 获取最后接种时间
	if len(c.VaccinationRecords) > 0 {
		lastVaccination := c.VaccinationRecords[len(c.VaccinationRecords)-1].VaccinationDate
		info.LastVaccination = &lastVaccination
	}

	return info
}

// GetFullAddress 获取完整地址
func (c *Child) GetFullAddress() string {
	if c.Province == "" && c.City == "" && c.District == "" {
		return c.Address
	}
	return c.Province + c.City + c.District + c.Address
}

// GenerateChildID 生成儿童ID
func GenerateChildID() string {
	// 格式：CHD + 年月日 + 6位随机数
	now := time.Now()
	return "CHD" + now.Format("20060102") + GenerateRandomString(6)
}

// GenerateRandomString 生成随机字符串
func GenerateRandomString(length int) string {
	const charset = "0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}
