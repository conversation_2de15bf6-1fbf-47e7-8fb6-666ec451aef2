package service

import (
	"errors"
	"fmt"
	"time"

	"labchemsafe/internal/config"
	"labchemsafe/internal/model"

	"gorm.io/gorm"
)

type ChildService struct {
	db *gorm.DB
}

func NewChildService() *ChildService {
	return &ChildService{
		db: config.GetDB(),
	}
}

// CreateChild 创建儿童档案
func (s *ChildService) CreateChild(req *model.ChildRequest, operatorID uint) (*model.Child, error) {
	// 检查监护人电话是否已存在同名儿童
	var existingChild model.Child
	if err := s.db.Where("name = ? AND guardian_phone = ?", req.Name, req.GuardianPhone).First(&existingChild).Error; err == nil {
		return nil, errors.New("该监护人已有同名儿童档案")
	}

	// 生成儿童ID
	childID := model.GenerateChildID()
	
	// 创建儿童档案
	child := &model.Child{
		ChildID:          childID,
		Name:             req.Name,
		Gender:           req.Gender,
		BirthDate:        req.BirthDate,
		BirthWeight:      req.BirthWeight,
		BirthHeight:      req.BirthHeight,
		BloodType:        req.BloodType,
		GuardianName:     req.GuardianName,
		GuardianPhone:    req.GuardianPhone,
		GuardianIDCard:   req.GuardianIDCard,
		GuardianRelation: req.GuardianRelation,
		Province:         req.Province,
		City:             req.City,
		District:         req.District,
		Address:          req.Address,
		Allergies:        req.Allergies,
		MedicalHistory:   req.MedicalHistory,
		FamilyHistory:    req.FamilyHistory,
		Status:           1,
		RegistrationDate: time.Now(),
		LastUpdateBy:     operatorID,
	}

	// TODO: 生成区块链地址和公私钥对
	child.BlockchainAddress = "0x" + model.GenerateRandomString(40)
	child.PublicKey = "public_key_" + model.GenerateRandomString(64)

	// 保存到数据库
	if err := s.db.Create(child).Error; err != nil {
		return nil, fmt.Errorf("创建儿童档案失败: %w", err)
	}

	// TODO: 将档案信息上链
	child.CreationTxHash = "0x" + model.GenerateRandomString(64)
	s.db.Model(child).Update("creation_tx_hash", child.CreationTxHash)

	return child, nil
}

// GetChildByID 根据ID获取儿童档案
func (s *ChildService) GetChildByID(id uint) (*model.Child, error) {
	var child model.Child
	if err := s.db.Preload("VaccinationRecords").Preload("HealthCheckups").First(&child, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("儿童档案不存在")
		}
		return nil, err
	}
	return &child, nil
}

// GetChildByChildID 根据儿童ID获取档案
func (s *ChildService) GetChildByChildID(childID string) (*model.Child, error) {
	var child model.Child
	if err := s.db.Preload("VaccinationRecords").Preload("HealthCheckups").Where("child_id = ?", childID).First(&child).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("儿童档案不存在")
		}
		return nil, err
	}
	return &child, nil
}

// GetChildList 获取儿童档案列表
func (s *ChildService) GetChildList(page, size int, keyword, gender, status string) (*model.PaginationResponse, error) {
	var children []model.Child
	var total int64

	query := s.db.Model(&model.Child{})

	// 关键词搜索
	if keyword != "" {
		query = query.Where("name LIKE ? OR child_id LIKE ? OR guardian_name LIKE ? OR guardian_phone LIKE ?",
			"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 性别筛选
	if gender != "" {
		query = query.Where("gender = ?", gender)
	}

	// 状态筛选
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 分页查询
	offset := (page - 1) * size
	if err := query.Preload("VaccinationRecords", func(db *gorm.DB) *gorm.DB {
		return db.Order("vaccination_date DESC").Limit(1)
	}).Offset(offset).Limit(size).Order("created_at DESC").Find(&children).Error; err != nil {
		return nil, err
	}

	// 转换为响应格式
	childInfos := make([]*model.ChildInfo, len(children))
	for i, child := range children {
		childInfos[i] = child.ToChildInfo()
	}

	return &model.PaginationResponse{
		Total: total,
		Page:  page,
		Size:  size,
		List:  childInfos,
	}, nil
}

// UpdateChild 更新儿童档案
func (s *ChildService) UpdateChild(id uint, updates map[string]interface{}, operatorID uint) error {
	// 移除不允许更新的字段
	delete(updates, "id")
	delete(updates, "child_id")
	delete(updates, "blockchain_address")
	delete(updates, "public_key")
	delete(updates, "creation_tx_hash")
	delete(updates, "created_at")
	delete(updates, "registration_date")

	// 添加更新信息
	updates["last_update_by"] = operatorID
	updates["updated_at"] = time.Now()

	// 更新数据库
	if err := s.db.Model(&model.Child{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新儿童档案失败: %w", err)
	}

	// TODO: 将更新信息上链
	return nil
}

// DeleteChild 删除儿童档案（软删除）
func (s *ChildService) DeleteChild(id uint, operatorID uint) error {
	// 检查是否有相关的接种记录
	var vaccinationCount int64
	if err := s.db.Model(&model.VaccinationRecord{}).Where("child_id = ?", id).Count(&vaccinationCount).Error; err != nil {
		return err
	}

	if vaccinationCount > 0 {
		return errors.New("该儿童有接种记录，无法删除")
	}

	// 软删除
	if err := s.db.Delete(&model.Child{}, id).Error; err != nil {
		return fmt.Errorf("删除儿童档案失败: %w", err)
	}

	return nil
}

// GetChildrenByGuardianPhone 根据监护人电话获取儿童列表
func (s *ChildService) GetChildrenByGuardianPhone(phone string) ([]model.Child, error) {
	var children []model.Child
	if err := s.db.Where("guardian_phone = ? AND status = 1", phone).Find(&children).Error; err != nil {
		return nil, err
	}
	return children, nil
}

// GetChildStatistics 获取儿童档案统计信息
func (s *ChildService) GetChildStatistics() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总儿童数
	var totalChildren int64
	s.db.Model(&model.Child{}).Where("status != -1").Count(&totalChildren)
	stats["total_children"] = totalChildren

	// 本月新增
	var monthlyNew int64
	s.db.Model(&model.Child{}).Where("registration_date >= ?", time.Now().AddDate(0, -1, 0)).Count(&monthlyNew)
	stats["monthly_new"] = monthlyNew

	// 性别分布
	var genderStats []map[string]interface{}
	s.db.Model(&model.Child{}).Select("gender, count(*) as count").Where("status != -1").Group("gender").Scan(&genderStats)
	stats["gender_distribution"] = genderStats

	// 年龄分布
	var ageStats []map[string]interface{}
	s.db.Raw(`
		SELECT 
			CASE 
				WHEN TIMESTAMPDIFF(MONTH, birth_date, NOW()) < 12 THEN '0-1岁'
				WHEN TIMESTAMPDIFF(MONTH, birth_date, NOW()) < 36 THEN '1-3岁'
				WHEN TIMESTAMPDIFF(MONTH, birth_date, NOW()) < 72 THEN '3-6岁'
				ELSE '6岁以上'
			END as age_group,
			COUNT(*) as count
		FROM children 
		WHERE deleted_at IS NULL AND status != -1
		GROUP BY age_group
	`).Scan(&ageStats)
	stats["age_distribution"] = ageStats

	return stats, nil
}

// ValidateChildData 验证儿童数据
func (s *ChildService) ValidateChildData(req *model.ChildRequest) error {
	// 验证出生日期不能是未来时间
	if req.BirthDate.After(time.Now()) {
		return errors.New("出生日期不能是未来时间")
	}

	// 验证年龄不能超过18岁
	age := time.Since(req.BirthDate).Hours() / 24 / 365
	if age > 18 {
		return errors.New("儿童年龄不能超过18岁")
	}

	// 验证监护人电话格式
	if len(req.GuardianPhone) != 11 {
		return errors.New("监护人电话格式不正确")
	}

	return nil
}
