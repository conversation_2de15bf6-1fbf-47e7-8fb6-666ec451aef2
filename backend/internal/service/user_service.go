package service

import (
	"errors"
	"time"

	"labchemsafe/internal/config"
	"labchemsafe/internal/model"
	"labchemsafe/internal/utils"

	"gorm.io/gorm"
)

type UserService struct {
	db *gorm.DB
}

func NewUserService() *UserService {
	return &UserService{
		db: config.GetDB(),
	}
}

// Register 用户注册
func (s *UserService) Register(req *model.RegisterRequest) (*model.User, error) {
	// 检查用户名是否已存在
	var existingUser model.User
	if err := s.db.Where("username = ? OR email = ?", req.Username, req.Email).First(&existingUser).Error; err == nil {
		return nil, errors.New("username or email already exists")
	}

	// 创建新用户
	user := &model.User{
		Username:   req.Username,
		Email:      req.Email,
		RealName:   req.RealName,
		Phone:      req.Phone,
		Department: req.Department,
		Position:   req.Position,
		Status:     1,
	}

	// 设置密码
	if err := user.SetPassword(req.Password); err != nil {
		return nil, err
	}

	// 保存用户
	if err := s.db.Create(user).Error; err != nil {
		return nil, err
	}

	// 分配默认角色（普通用户）
	var defaultRole model.Role
	if err := s.db.Where("name = ?", "user").First(&defaultRole).Error; err == nil {
		s.db.Model(user).Association("Roles").Append(&defaultRole)
	}

	return user, nil
}

// Login 用户登录
func (s *UserService) Login(req *model.LoginRequest) (*model.LoginResponse, error) {
	var user model.User
	if err := s.db.Preload("Roles").Where("username = ? OR email = ?", req.Username, req.Username).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("user not found")
		}
		return nil, err
	}

	// 检查用户状态
	if user.Status != 1 {
		return nil, errors.New("user account is disabled")
	}

	// 验证密码
	if !user.CheckPassword(req.Password) {
		return nil, errors.New("invalid password")
	}

	// 更新最后登录时间
	s.db.Model(&user).Update("last_login_at", time.Now())

	// 生成JWT token
	roles := make([]string, len(user.Roles))
	for i, role := range user.Roles {
		roles[i] = role.Name
	}

	token, err := utils.GenerateToken(user.ID, user.Username, roles, 24*time.Hour)
	if err != nil {
		return nil, err
	}

	return &model.LoginResponse{
		Token: token,
		User:  user.ToUserInfo(),
	}, nil
}

// GetUserByID 根据ID获取用户
func (s *UserService) GetUserByID(id uint) (*model.User, error) {
	var user model.User
	if err := s.db.Preload("Roles").First(&user, id).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

// GetUserProfile 获取用户资料
func (s *UserService) GetUserProfile(userID uint) (*model.UserInfo, error) {
	user, err := s.GetUserByID(userID)
	if err != nil {
		return nil, err
	}
	return user.ToUserInfo(), nil
}

// UpdateUserProfile 更新用户资料
func (s *UserService) UpdateUserProfile(userID uint, updates map[string]interface{}) error {
	// 移除不允许更新的字段
	delete(updates, "id")
	delete(updates, "username")
	delete(updates, "password_hash")
	delete(updates, "created_at")

	return s.db.Model(&model.User{}).Where("id = ?", userID).Updates(updates).Error
}

// ChangePassword 修改密码
func (s *UserService) ChangePassword(userID uint, oldPassword, newPassword string) error {
	var user model.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return err
	}

	// 验证旧密码
	if !user.CheckPassword(oldPassword) {
		return errors.New("invalid old password")
	}

	// 设置新密码
	if err := user.SetPassword(newPassword); err != nil {
		return err
	}

	return s.db.Model(&user).Update("password_hash", user.PasswordHash).Error
}

// GetUserList 获取用户列表
func (s *UserService) GetUserList(page, size int, keyword string) (*model.PaginationResponse, error) {
	var users []model.User
	var total int64

	query := s.db.Model(&model.User{}).Preload("Roles")

	// 关键词搜索
	if keyword != "" {
		query = query.Where("username LIKE ? OR real_name LIKE ? OR email LIKE ?",
			"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 分页查询
	offset := (page - 1) * size
	if err := query.Offset(offset).Limit(size).Find(&users).Error; err != nil {
		return nil, err
	}

	// 转换为用户信息
	userInfos := make([]*model.UserInfo, len(users))
	for i, user := range users {
		userInfos[i] = user.ToUserInfo()
	}

	return &model.PaginationResponse{
		Total: total,
		Page:  page,
		Size:  size,
		List:  userInfos,
	}, nil
}
