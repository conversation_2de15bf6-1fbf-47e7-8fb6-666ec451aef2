package utils

import (
	"context"
	"crypto/ecdsa"
	"encoding/hex"
	"errors"
	"fmt"
	"math/big"
	"time"

	"labchemsafe/internal/config"

	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/crypto"
)

// BlockchainClient 区块链客户端包装
// 说明：为便于演示，这里使用签名并构造交易哈希的流程；
// 生产中应接入真实RPC与合约ABI调用。
type BlockchainClient struct {
	cfg config.BlockchainConfig
}

func NewBlockchainClient(cfg config.BlockchainConfig) *BlockchainClient {
	return &BlockchainClient{cfg: cfg}
}

// SubmitRecord 将数据上链，返回交易哈希
// dataHash 通常为业务数据的哈希（如Keccak256/sha256）
func (bc *BlockchainClient) SubmitRecord(ctx context.Context, dataHash []byte, tag string) (string, error) {
	if !bc.cfg.Enabled {
		return "", errors.New("blockchain disabled")
	}

	// 使用私钥离线签名生成一个可追溯的标识（模拟交易hash格式）
	if bc.cfg.PrivateKey == "" {
		return "", errors.New("missing blockchain private key")
	}
	pk, err := crypto.HexToECDSA(strip0x(bc.cfg.PrivateKey))
	if err != nil {
		return "", fmt.Errorf("invalid private key: %w", err)
	}

	// 生成签名
	sig, err := signHash(pk, dataHash)
	if err != nil {
		return "", err
	}

	// 组合一个“交易哈希”
	fakeTx := crypto.Keccak256Hash(append(dataHash, sig...))
	return fakeTx.Hex(), nil
}

// signHash 对给定哈希进行签名
func signHash(pk *ecdsa.PrivateKey, h []byte) ([]byte, error) {
	sig, err := crypto.Sign(h, pk)
	if err != nil {
		return nil, err
	}
	return sig, nil
}

// BuildTransactor 构造交易参数（预留真实链交互）
func (bc *BlockchainClient) BuildTransactor() (*bind.TransactOpts, error) {
	if bc.cfg.PrivateKey == "" {
		return nil, errors.New("missing blockchain private key")
	}
	pk, err := crypto.HexToECDSA(strip0x(bc.cfg.PrivateKey))
	if err != nil {
		return nil, err
	}
	opts := bind.NewKeyedTransactor(pk)
	if bc.cfg.GasLimit > 0 {
		opts.GasLimit = bc.cfg.GasLimit
	}
	return opts, nil
}

func strip0x(s string) string {
	if len(s) >= 2 && s[:2] == "0x" {
		return s[2:]
	}
	return s
}

// ComputeDataHash 统一的数据哈希接口
func ComputeDataHash(parts ...[]byte) []byte {
	buf := []byte{}
	for _, p := range parts {
		buf = append(buf, p...)
	}
	h := crypto.Keccak256(buf)
	return h
}

// Hex returns 0x-prefixed hex
func Hex(b []byte) string { return "0x" + hex.EncodeToString(b) }

// WaitConfirmations 预留：等待确认数
func (bc *BlockchainClient) WaitConfirmations(ctx context.Context, txHash string) error {
	// 真实实现应轮询RPC确认区块高度。
	// 这里做最小等待以避免阻塞示例。
	select {
	case <-time.After(100 * time.Millisecond):
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

