{"name": "child-health-tracker-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^6.28.1", "@ant-design/icons": "^5.5.1", "antd": "^5.22.6", "axios": "^1.7.9", "dayjs": "^1.11.13", "qrcode": "^1.5.4", "qrcode-reader": "^1.0.4", "recharts": "^2.13.3", "zustand": "^5.0.2", "@types/qrcode": "^1.5.5"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}