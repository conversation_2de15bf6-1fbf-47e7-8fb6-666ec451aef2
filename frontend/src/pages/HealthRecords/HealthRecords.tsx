import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  DatePicker,
  Modal,
  Form,
  Row,
  Col,
  Tag,
  Typography,
  Divider,
  Steps,
  Timeline,
  Badge,
  Tooltip,
  message,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  MedicineBoxOutlined,
  FileTextOutlined,
  UserOutlined,
  SafetyCertificateOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { Step } = Steps;

interface HealthRecord {
  key: string;
  id: string;
  childId: string;
  childName: string;
  serviceType: 'vaccination' | 'checkup';
  serviceName: string;
  serviceDate: string;
  location: string;
  operator: string;
  status: 'pending' | 'confirmed' | 'completed';
  batchNumber?: string;
  blockchainHash?: string;
  guardianConfirmed: boolean;
  operatorSigned: boolean;
}

const HealthRecords: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<HealthRecord | null>(null);
  const [form] = Form.useForm();

  // 模拟数据
  const mockData: HealthRecord[] = [
    {
      key: '1',
      id: 'HR202401001',
      childId: 'CHD202401001',
      childName: '张小明',
      serviceType: 'vaccination',
      serviceName: '乙型肝炎疫苗（第1剂）',
      serviceDate: '2024-01-15',
      location: '朝阳医院接种点',
      operator: '李护士',
      status: 'completed',
      batchNumber: 'HBV20240101-A001',
      blockchainHash: '0x1234567890abcdef...',
      guardianConfirmed: true,
      operatorSigned: true,
    },
    {
      key: '2',
      id: 'HR202401002',
      childId: 'CHD202401002',
      childName: '李小红',
      serviceType: 'checkup',
      serviceName: '6个月体格检查',
      serviceDate: '2024-01-14',
      location: '社区卫生服务中心',
      operator: '王医生',
      status: 'confirmed',
      guardianConfirmed: true,
      operatorSigned: true,
    },
    {
      key: '3',
      id: 'HR202401003',
      childId: 'CHD202401003',
      childName: '王小华',
      serviceType: 'vaccination',
      serviceName: '脊髓灰质炎疫苗（第2剂）',
      serviceDate: '2024-01-13',
      location: '区疾控中心',
      operator: '张护士',
      status: 'pending',
      batchNumber: 'IPV20240105-B002',
      guardianConfirmed: false,
      operatorSigned: true,
    },
  ];

  const [dataSource, setDataSource] = useState<HealthRecord[]>(mockData);

  const columns: ColumnsType<HealthRecord> = [
    {
      title: '记录ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
      render: (text) => <Text code>{text}</Text>,
    },
    {
      title: '儿童信息',
      key: 'child',
      width: 150,
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Text strong>{record.childName}</Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.childId}
          </Text>
        </Space>
      ),
    },
    {
      title: '服务类型',
      dataIndex: 'serviceType',
      key: 'serviceType',
      width: 100,
      render: (type) => {
        const config = {
          vaccination: { color: 'blue', icon: <MedicineBoxOutlined />, text: '疫苗接种' },
          checkup: { color: 'green', icon: <FileTextOutlined />, text: '体格检查' },
        };
        const item = config[type as keyof typeof config];
        return (
          <Tag color={item.color} icon={item.icon}>
            {item.text}
          </Tag>
        );
      },
    },
    {
      title: '服务内容',
      dataIndex: 'serviceName',
      key: 'serviceName',
      width: 200,
    },
    {
      title: '服务日期',
      dataIndex: 'serviceDate',
      key: 'serviceDate',
      width: 100,
      render: (date) => dayjs(date).format('MM-DD'),
    },
    {
      title: '服务地点',
      dataIndex: 'location',
      key: 'location',
      width: 150,
    },
    {
      title: '操作人员',
      dataIndex: 'operator',
      key: 'operator',
      width: 100,
      render: (operator) => (
        <Space>
          <UserOutlined />
          {operator}
        </Space>
      ),
    },
    {
      title: '确认状态',
      key: 'confirmation',
      width: 120,
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Space size="small">
            <Badge
              status={record.operatorSigned ? 'success' : 'default'}
              text="医护"
            />
            <Badge
              status={record.guardianConfirmed ? 'success' : 'processing'}
              text="家长"
            />
          </Space>
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => {
        const config = {
          pending: { color: 'orange', text: '待确认' },
          confirmed: { color: 'blue', text: '已确认' },
          completed: { color: 'green', text: '已完成' },
        };
        const item = config[status as keyof typeof config];
        return <Tag color={item.color}>{item.text}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          {record.status === 'pending' && (
            <Tooltip title="确认记录">
              <Button
                type="text"
                icon={<CheckCircleOutlined />}
                onClick={() => handleConfirm(record)}
              />
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    form.resetFields();
    setSelectedRecord(null);
    setModalVisible(true);
  };

  const handleViewDetail = (record: HealthRecord) => {
    setSelectedRecord(record);
    setDetailModalVisible(true);
  };

  const handleConfirm = (record: HealthRecord) => {
    Modal.confirm({
      title: '确认服务记录',
      content: `确认要将 ${record.childName} 的 ${record.serviceName} 记录上链吗？`,
      onOk() {
        // 模拟确认操作
        const updatedData = dataSource.map(item => 
          item.key === record.key 
            ? { ...item, status: 'confirmed' as const, guardianConfirmed: true }
            : item
        );
        setDataSource(updatedData);
        message.success('记录确认成功，已提交到区块链');
      },
    });
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      const newRecord: HealthRecord = {
        key: Date.now().toString(),
        id: `HR${Date.now()}`,
        ...values,
        serviceDate: values.serviceDate.format('YYYY-MM-DD'),
        status: 'pending' as const,
        guardianConfirmed: false,
        operatorSigned: true,
      };

      setDataSource([newRecord, ...dataSource]);
      setModalVisible(false);
      message.success('服务记录创建成功');
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  return (
    <div>
      <Title level={2}>预防保健服务记录可信上链</Title>
      
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8} md={6}>
            <Input
              placeholder="搜索儿童姓名或ID"
              prefix={<SearchOutlined />}
              allowClear
            />
          </Col>
          <Col xs={24} sm={8} md={6}>
            <Select placeholder="服务类型" allowClear style={{ width: '100%' }}>
              <Option value="vaccination">疫苗接种</Option>
              <Option value="checkup">体格检查</Option>
            </Select>
          </Col>
          <Col xs={24} sm={8} md={6}>
            <Select placeholder="记录状态" allowClear style={{ width: '100%' }}>
              <Option value="pending">待确认</Option>
              <Option value="confirmed">已确认</Option>
              <Option value="completed">已完成</Option>
            </Select>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <RangePicker placeholder={['开始日期', '结束日期']} style={{ width: '100%' }} />
          </Col>
        </Row>
        
        <Divider />
        
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                新增服务记录
              </Button>
              <Button>
                批量确认
              </Button>
            </Space>
          </Col>
          <Col>
            <span>共 {dataSource.length} 条记录</span>
          </Col>
        </Row>
      </Card>

      <Card>
        <Table
          columns={columns}
          dataSource={dataSource}
          loading={loading}
          scroll={{ x: 1200 }}
          pagination={{
            total: dataSource.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
        />
      </Card>

      {/* 新增记录模态框 */}
      <Modal
        title="新增服务记录"
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => setModalVisible(false)}
        width={800}
        destroyOnClose
      >
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="childId"
                label="儿童ID"
                rules={[{ required: true, message: '请输入儿童ID' }]}
              >
                <Input placeholder="请输入或扫描儿童ID" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="childName"
                label="儿童姓名"
                rules={[{ required: true, message: '请输入儿童姓名' }]}
              >
                <Input placeholder="请输入儿童姓名" />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="serviceType"
                label="服务类型"
                rules={[{ required: true, message: '请选择服务类型' }]}
              >
                <Select placeholder="请选择服务类型">
                  <Option value="vaccination">疫苗接种</Option>
                  <Option value="checkup">体格检查</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="serviceDate"
                label="服务日期"
                rules={[{ required: true, message: '请选择服务日期' }]}
              >
                <DatePicker style={{ width: '100%' }} placeholder="请选择服务日期" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="serviceName"
            label="服务内容"
            rules={[{ required: true, message: '请输入服务内容' }]}
          >
            <Input placeholder="请输入具体的服务内容" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="location"
                label="服务地点"
                rules={[{ required: true, message: '请输入服务地点' }]}
              >
                <Input placeholder="请输入服务地点" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="operator"
                label="操作人员"
                rules={[{ required: true, message: '请输入操作人员' }]}
              >
                <Input placeholder="请输入操作人员姓名" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="batchNumber"
            label="疫苗批次号"
            tooltip="仅疫苗接种需要填写"
          >
            <Input placeholder="请输入疫苗批次号（如适用）" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 详情查看模态框 */}
      <Modal
        title="服务记录详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedRecord && (
          <div>
            {/* 基本信息 */}
            <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]}>
                <Col span={8}>
                  <Text strong>记录ID：</Text><br />
                  <Text code>{selectedRecord.id}</Text>
                </Col>
                <Col span={8}>
                  <Text strong>儿童信息：</Text><br />
                  <Text>{selectedRecord.childName} ({selectedRecord.childId})</Text>
                </Col>
                <Col span={8}>
                  <Text strong>服务日期：</Text><br />
                  <Text>{selectedRecord.serviceDate}</Text>
                </Col>
                <Col span={12}>
                  <Text strong>服务内容：</Text><br />
                  <Text>{selectedRecord.serviceName}</Text>
                </Col>
                <Col span={12}>
                  <Text strong>服务地点：</Text><br />
                  <Text>{selectedRecord.location}</Text>
                </Col>
                {selectedRecord.batchNumber && (
                  <Col span={12}>
                    <Text strong>疫苗批次号：</Text><br />
                    <Text code>{selectedRecord.batchNumber}</Text>
                  </Col>
                )}
              </Row>
            </Card>

            {/* 确认流程 */}
            <Card title="确认流程" size="small" style={{ marginBottom: 16 }}>
              <Steps current={selectedRecord.status === 'completed' ? 2 : selectedRecord.status === 'confirmed' ? 1 : 0}>
                <Step 
                  title="医护确认" 
                  description="操作人员数字签名"
                  icon={selectedRecord.operatorSigned ? <CheckCircleOutlined /> : <ClockCircleOutlined />}
                />
                <Step 
                  title="家长确认" 
                  description="监护人数字签名"
                  icon={selectedRecord.guardianConfirmed ? <CheckCircleOutlined /> : <ClockCircleOutlined />}
                />
                <Step 
                  title="上链完成" 
                  description="记录写入区块链"
                  icon={selectedRecord.blockchainHash ? <SafetyCertificateOutlined /> : <ClockCircleOutlined />}
                />
              </Steps>
            </Card>

            {/* 区块链信息 */}
            {selectedRecord.blockchainHash && (
              <Card title="区块链信息" size="small">
                <Row gutter={[16, 16]}>
                  <Col span={24}>
                    <Text strong>交易哈希：</Text><br />
                    <Text code copyable>{selectedRecord.blockchainHash}</Text>
                  </Col>
                  <Col span={12}>
                    <Text strong>上链时间：</Text><br />
                    <Text>{dayjs().format('YYYY-MM-DD HH:mm:ss')}</Text>
                  </Col>
                  <Col span={12}>
                    <Text strong>区块高度：</Text><br />
                    <Text>1,234,567</Text>
                  </Col>
                </Row>
              </Card>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default HealthRecords;
