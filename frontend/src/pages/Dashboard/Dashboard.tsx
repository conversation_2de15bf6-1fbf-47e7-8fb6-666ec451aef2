import React from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Progress,
  Table,
  Tag,
  Space,
  Typography,
  Alert,
  List,
  Avatar,
} from 'antd';
import {
  UserOutlined,
  MedicineBoxOutlined,
  FileTextOutlined,
  WarningOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
} from '@ant-design/icons';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';

const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  // 模拟数据
  const statisticsData = [
    {
      title: '注册儿童总数',
      value: 12580,
      prefix: <UserOutlined />,
      valueStyle: { color: '#3f8600' },
      suffix: <ArrowUpOutlined style={{ color: '#3f8600' }} />,
    },
    {
      title: '本月接种人次',
      value: 2456,
      prefix: <MedicineBoxOutlined />,
      valueStyle: { color: '#1890ff' },
      suffix: <ArrowUpOutlined style={{ color: '#3f8600' }} />,
    },
    {
      title: '疫苗库存批次',
      value: 156,
      prefix: <FileTextOutlined />,
      valueStyle: { color: '#722ed1' },
    },
    {
      title: '风险预警',
      value: 3,
      prefix: <WarningOutlined />,
      valueStyle: { color: '#cf1322' },
      suffix: <ArrowDownOutlined style={{ color: '#cf1322' }} />,
    },
  ];

  // 接种率数据
  const vaccinationData = [
    { name: '1月', 接种率: 95.2, 目标: 95 },
    { name: '2月', 接种率: 96.1, 目标: 95 },
    { name: '3月', 接种率: 94.8, 目标: 95 },
    { name: '4月', 接种率: 97.3, 目标: 95 },
    { name: '5月', 接种率: 96.8, 目标: 95 },
    { name: '6月', 接种率: 98.1, 目标: 95 },
  ];

  // 疫苗类型分布
  const vaccineTypeData = [
    { name: '乙肝疫苗', value: 2340, color: '#1890ff' },
    { name: '脊髓灰质炎疫苗', value: 1890, color: '#52c41a' },
    { name: '百白破疫苗', value: 1560, color: '#faad14' },
    { name: '麻疹疫苗', value: 1230, color: '#722ed1' },
    { name: '其他', value: 980, color: '#fa8c16' },
  ];

  // 最近活动
  const recentActivities = [
    {
      title: '新增儿童档案',
      description: '张小明 (ID: CHD202401001) 完成注册',
      time: '2分钟前',
      avatar: <Avatar icon={<UserOutlined />} style={{ backgroundColor: '#1890ff' }} />,
    },
    {
      title: '疫苗接种完成',
      description: '李小红完成乙肝疫苗第二针接种',
      time: '5分钟前',
      avatar: <Avatar icon={<MedicineBoxOutlined />} style={{ backgroundColor: '#52c41a' }} />,
    },
    {
      title: '风险预警',
      description: '批次号 VB20240315 疫苗温度异常',
      time: '10分钟前',
      avatar: <Avatar icon={<WarningOutlined />} style={{ backgroundColor: '#ff4d4f' }} />,
    },
    {
      title: '档案更新',
      description: '王小华过敏史信息已更新',
      time: '15分钟前',
      avatar: <Avatar icon={<FileTextOutlined />} style={{ backgroundColor: '#722ed1' }} />,
    },
  ];

  // 待办事项表格数据
  const todoColumns = [
    {
      title: '事项',
      dataIndex: 'task',
      key: 'task',
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority: string) => {
        const color = priority === '高' ? 'red' : priority === '中' ? 'orange' : 'green';
        return <Tag color={color}>{priority}</Tag>;
      },
    },
    {
      title: '截止时间',
      dataIndex: 'deadline',
      key: 'deadline',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const color = status === '进行中' ? 'processing' : status === '已完成' ? 'success' : 'default';
        return <Tag color={color}>{status}</Tag>;
      },
    },
  ];

  const todoData = [
    {
      key: '1',
      task: '审核新增儿童档案',
      priority: '高',
      deadline: '2024-01-15',
      status: '进行中',
    },
    {
      key: '2',
      task: '疫苗库存盘点',
      priority: '中',
      deadline: '2024-01-20',
      status: '待开始',
    },
    {
      key: '3',
      task: '月度接种报告',
      priority: '中',
      deadline: '2024-01-25',
      status: '进行中',
    },
  ];

  return (
    <div>
      <Title level={2}>工作台</Title>
      
      {/* 预警信息 */}
      <Alert
        message="系统提醒"
        description="发现3个疫苗批次存在温度异常，请及时处理。"
        type="warning"
        showIcon
        closable
        style={{ marginBottom: 24 }}
      />

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        {statisticsData.map((item, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card>
              <Statistic
                title={item.title}
                value={item.value}
                prefix={item.prefix}
                suffix={item.suffix}
                valueStyle={item.valueStyle}
              />
            </Card>
          </Col>
        ))}
      </Row>

      <Row gutter={[16, 16]}>
        {/* 接种率趋势 */}
        <Col xs={24} lg={16}>
          <Card title="接种率趋势" style={{ marginBottom: 16 }}>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={vaccinationData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="接种率" stroke="#1890ff" strokeWidth={2} />
                <Line type="monotone" dataKey="目标" stroke="#52c41a" strokeDasharray="5 5" />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        {/* 疫苗类型分布 */}
        <Col xs={24} lg={8}>
          <Card title="疫苗类型分布" style={{ marginBottom: 16 }}>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={vaccineTypeData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {vaccineTypeData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        {/* 最近活动 */}
        <Col xs={24} lg={12}>
          <Card title="最近活动">
            <List
              itemLayout="horizontal"
              dataSource={recentActivities}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={item.avatar}
                    title={item.title}
                    description={
                      <Space direction="vertical" size={0}>
                        <Text>{item.description}</Text>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {item.time}
                        </Text>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 待办事项 */}
        <Col xs={24} lg={12}>
          <Card title="待办事项">
            <Table
              columns={todoColumns}
              dataSource={todoData}
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
