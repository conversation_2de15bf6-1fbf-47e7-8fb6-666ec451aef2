import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  DatePicker,
  Modal,
  Form,
  Row,
  Col,
  Tag,
  Avatar,
  Tooltip,
  Popconfirm,
  Typography,
  Divider,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  QrcodeOutlined,
  UserOutlined,
  DownloadOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface ChildRecord {
  key: string;
  id: string;
  name: string;
  gender: string;
  birthDate: string;
  guardianName: string;
  guardianPhone: string;
  address: string;
  status: string;
  registrationDate: string;
  lastVaccination: string;
  allergies: string[];
}

const ChildManagement: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<ChildRecord | null>(null);
  const [form] = Form.useForm();

  // 模拟数据
  const mockData: ChildRecord[] = [
    {
      key: '1',
      id: 'CHD202401001',
      name: '张小明',
      gender: '男',
      birthDate: '2023-03-15',
      guardianName: '张华',
      guardianPhone: '13800138001',
      address: '北京市朝阳区xxx街道',
      status: '正常',
      registrationDate: '2023-03-16',
      lastVaccination: '2024-01-10',
      allergies: ['青霉素'],
    },
    {
      key: '2',
      id: 'CHD202401002',
      name: '李小红',
      gender: '女',
      birthDate: '2023-05-20',
      guardianName: '李强',
      guardianPhone: '13800138002',
      address: '上海市浦东新区xxx路',
      status: '正常',
      registrationDate: '2023-05-21',
      lastVaccination: '2024-01-08',
      allergies: [],
    },
    {
      key: '3',
      id: 'CHD202401003',
      name: '王小华',
      gender: '男',
      birthDate: '2023-01-10',
      guardianName: '王敏',
      guardianPhone: '13800138003',
      address: '广州市天河区xxx大道',
      status: '暂停',
      registrationDate: '2023-01-11',
      lastVaccination: '2023-12-15',
      allergies: ['鸡蛋', '海鲜'],
    },
  ];

  const [dataSource, setDataSource] = useState<ChildRecord[]>(mockData);

  const columns: ColumnsType<ChildRecord> = [
    {
      title: '儿童ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
      render: (text) => (
        <Space>
          <Avatar size="small" icon={<UserOutlined />} />
          <span style={{ fontFamily: 'monospace' }}>{text}</span>
        </Space>
      ),
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 100,
    },
    {
      title: '性别',
      dataIndex: 'gender',
      key: 'gender',
      width: 60,
      render: (gender) => (
        <Tag color={gender === '男' ? 'blue' : 'pink'}>{gender}</Tag>
      ),
    },
    {
      title: '出生日期',
      dataIndex: 'birthDate',
      key: 'birthDate',
      width: 100,
      render: (date) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: '监护人',
      dataIndex: 'guardianName',
      key: 'guardianName',
      width: 100,
    },
    {
      title: '联系电话',
      dataIndex: 'guardianPhone',
      key: 'guardianPhone',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status) => {
        const color = status === '正常' ? 'green' : status === '暂停' ? 'red' : 'orange';
        return <Tag color={color}>{status}</Tag>;
      },
    },
    {
      title: '过敏史',
      dataIndex: 'allergies',
      key: 'allergies',
      width: 120,
      render: (allergies: string[]) => (
        <Space wrap>
          {allergies.length > 0 ? (
            allergies.map((allergy) => (
              <Tag key={allergy} color="orange" size="small">
                {allergy}
              </Tag>
            ))
          ) : (
            <Tag color="green" size="small">无</Tag>
          )}
        </Space>
      ),
    },
    {
      title: '最后接种',
      dataIndex: 'lastVaccination',
      key: 'lastVaccination',
      width: 100,
      render: (date) => dayjs(date).format('MM-DD'),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="生成二维码">
            <Button
              type="text"
              icon={<QrcodeOutlined />}
              onClick={() => handleGenerateQR(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDelete(record.key)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    form.resetFields();
    setSelectedRecord(null);
    setModalVisible(true);
  };

  const handleEdit = (record: ChildRecord) => {
    setSelectedRecord(record);
    form.setFieldsValue({
      ...record,
      birthDate: dayjs(record.birthDate),
    });
    setModalVisible(true);
  };

  const handleViewDetail = (record: ChildRecord) => {
    setSelectedRecord(record);
    setDetailModalVisible(true);
  };

  const handleDelete = (key: string) => {
    setDataSource(dataSource.filter(item => item.key !== key));
  };

  const handleGenerateQR = (record: ChildRecord) => {
    Modal.info({
      title: '儿童健康身份二维码',
      content: (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <div style={{ 
            width: 200, 
            height: 200, 
            border: '1px solid #d9d9d9', 
            margin: '0 auto 16px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#f5f5f5'
          }}>
            <QrcodeOutlined style={{ fontSize: 80, color: '#1890ff' }} />
          </div>
          <p>儿童ID: {record.id}</p>
          <p>姓名: {record.name}</p>
          <Button type="primary" icon={<DownloadOutlined />}>
            下载二维码
          </Button>
        </div>
      ),
      width: 400,
    });
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      const newRecord: ChildRecord = {
        key: selectedRecord ? selectedRecord.key : Date.now().toString(),
        id: selectedRecord ? selectedRecord.id : `CHD${Date.now()}`,
        ...values,
        birthDate: values.birthDate.format('YYYY-MM-DD'),
        registrationDate: selectedRecord ? selectedRecord.registrationDate : dayjs().format('YYYY-MM-DD'),
        lastVaccination: selectedRecord ? selectedRecord.lastVaccination : '',
        allergies: values.allergies || [],
      };

      if (selectedRecord) {
        setDataSource(dataSource.map(item => 
          item.key === selectedRecord.key ? newRecord : item
        ));
      } else {
        setDataSource([...dataSource, newRecord]);
      }

      setModalVisible(false);
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  return (
    <div>
      <Title level={2}>儿童数字健康身份与档案管理</Title>
      
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8} md={6}>
            <Input
              placeholder="搜索儿童姓名或ID"
              prefix={<SearchOutlined />}
              allowClear
            />
          </Col>
          <Col xs={24} sm={8} md={6}>
            <Select placeholder="选择性别" allowClear style={{ width: '100%' }}>
              <Option value="男">男</Option>
              <Option value="女">女</Option>
            </Select>
          </Col>
          <Col xs={24} sm={8} md={6}>
            <Select placeholder="选择状态" allowClear style={{ width: '100%' }}>
              <Option value="正常">正常</Option>
              <Option value="暂停">暂停</Option>
              <Option value="异常">异常</Option>
            </Select>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <RangePicker placeholder={['开始日期', '结束日期']} style={{ width: '100%' }} />
          </Col>
        </Row>
        
        <Divider />
        
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                新增儿童档案
              </Button>
              <Button icon={<DownloadOutlined />}>
                导出数据
              </Button>
            </Space>
          </Col>
          <Col>
            <span>共 {dataSource.length} 条记录</span>
          </Col>
        </Row>
      </Card>

      <Card>
        <Table
          columns={columns}
          dataSource={dataSource}
          loading={loading}
          scroll={{ x: 1200 }}
          pagination={{
            total: dataSource.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
        />
      </Card>

      {/* 新增/编辑模态框 */}
      <Modal
        title={selectedRecord ? '编辑儿童档案' : '新增儿童档案'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => setModalVisible(false)}
        width={800}
        destroyOnClose
      >
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="儿童姓名"
                rules={[{ required: true, message: '请输入儿童姓名' }]}
              >
                <Input placeholder="请输入儿童姓名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="gender"
                label="性别"
                rules={[{ required: true, message: '请选择性别' }]}
              >
                <Select placeholder="请选择性别">
                  <Option value="男">男</Option>
                  <Option value="女">女</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="birthDate"
                label="出生日期"
                rules={[{ required: true, message: '请选择出生日期' }]}
              >
                <DatePicker style={{ width: '100%' }} placeholder="请选择出生日期" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select placeholder="请选择状态">
                  <Option value="正常">正常</Option>
                  <Option value="暂停">暂停</Option>
                  <Option value="异常">异常</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="guardianName"
                label="监护人姓名"
                rules={[{ required: true, message: '请输入监护人姓名' }]}
              >
                <Input placeholder="请输入监护人姓名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="guardianPhone"
                label="监护人电话"
                rules={[
                  { required: true, message: '请输入监护人电话' },
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
                ]}
              >
                <Input placeholder="请输入监护人电话" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="address"
            label="居住地址"
            rules={[{ required: true, message: '请输入居住地址' }]}
          >
            <Input.TextArea rows={2} placeholder="请输入居住地址" />
          </Form.Item>

          <Form.Item
            name="allergies"
            label="过敏史"
          >
            <Select
              mode="tags"
              placeholder="请输入过敏物质，支持多选"
              style={{ width: '100%' }}
            >
              <Option value="青霉素">青霉素</Option>
              <Option value="鸡蛋">鸡蛋</Option>
              <Option value="海鲜">海鲜</Option>
              <Option value="花生">花生</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 详情查看模态框 */}
      <Modal
        title="儿童档案详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={600}
      >
        {selectedRecord && (
          <div>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <strong>儿童ID：</strong>{selectedRecord.id}
              </Col>
              <Col span={12}>
                <strong>姓名：</strong>{selectedRecord.name}
              </Col>
              <Col span={12}>
                <strong>性别：</strong>{selectedRecord.gender}
              </Col>
              <Col span={12}>
                <strong>出生日期：</strong>{selectedRecord.birthDate}
              </Col>
              <Col span={12}>
                <strong>监护人：</strong>{selectedRecord.guardianName}
              </Col>
              <Col span={12}>
                <strong>联系电话：</strong>{selectedRecord.guardianPhone}
              </Col>
              <Col span={24}>
                <strong>居住地址：</strong>{selectedRecord.address}
              </Col>
              <Col span={12}>
                <strong>注册日期：</strong>{selectedRecord.registrationDate}
              </Col>
              <Col span={12}>
                <strong>最后接种：</strong>{selectedRecord.lastVaccination}
              </Col>
              <Col span={24}>
                <strong>过敏史：</strong>
                <Space wrap style={{ marginLeft: 8 }}>
                  {selectedRecord.allergies.length > 0 ? (
                    selectedRecord.allergies.map((allergy) => (
                      <Tag key={allergy} color="orange">{allergy}</Tag>
                    ))
                  ) : (
                    <Tag color="green">无</Tag>
                  )}
                </Space>
              </Col>
            </Row>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ChildManagement;
