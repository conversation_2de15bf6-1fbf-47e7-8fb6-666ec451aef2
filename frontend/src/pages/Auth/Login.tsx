import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Form,
  Input,
  Button,
  Card,
  Typography,
  Space,
  Divider,
  message,
  Row,
  Col,
} from 'antd';
import {
  UserOutlined,
  LockOutlined,
  SafetyCertificateOutlined,
  MobileOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const navigate = useNavigate();

  const handleLogin = async (values: any) => {
    setLoading(true);
    try {
      // 模拟登录API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟登录成功
      message.success('登录成功');
      navigate('/dashboard');
    } catch (error) {
      message.error('登录失败，请检查用户名和密码');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <Row style={{ width: '100%', maxWidth: '1200px' }}>
        <Col xs={24} lg={12} style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <div style={{ textAlign: 'center', color: 'white', padding: '40px' }}>
            <SafetyCertificateOutlined style={{ fontSize: '80px', marginBottom: '24px' }} />
            <Title level={1} style={{ color: 'white', marginBottom: '16px' }}>
              儿童预防保健信息追溯管理系统
            </Title>
            <Title level={3} style={{ color: 'rgba(255,255,255,0.8)', fontWeight: 'normal' }}>
              Child Health Tracker
            </Title>
            <Text style={{ color: 'rgba(255,255,255,0.7)', fontSize: '16px' }}>
              基于区块链技术的儿童健康档案管理平台
            </Text>
          </div>
        </Col>
        
        <Col xs={24} lg={12} style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Card
            style={{
              width: '100%',
              maxWidth: '400px',
              boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
              borderRadius: '16px',
            }}
          >
            <div style={{ textAlign: 'center', marginBottom: '32px' }}>
              <Title level={2} style={{ marginBottom: '8px' }}>
                系统登录
              </Title>
              <Text type="secondary">
                请使用您的账户信息登录系统
              </Text>
            </div>

            <Form
              form={form}
              name="login"
              onFinish={handleLogin}
              layout="vertical"
              size="large"
            >
              <Form.Item
                name="username"
                rules={[{ required: true, message: '请输入用户名' }]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="用户名"
                  autoComplete="username"
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[{ required: true, message: '请输入密码' }]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="密码"
                  autoComplete="current-password"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  block
                  style={{ height: '48px', fontSize: '16px' }}
                >
                  登录
                </Button>
              </Form.Item>
            </Form>

            <Divider>或</Divider>

            <Space direction="vertical" style={{ width: '100%' }}>
              <Button
                icon={<MobileOutlined />}
                block
                style={{ height: '48px' }}
              >
                手机验证码登录
              </Button>
              
              <div style={{ textAlign: 'center', marginTop: '16px' }}>
                <Space split={<Divider type="vertical" />}>
                  <Button type="link" size="small">
                    忘记密码
                  </Button>
                  <Button type="link" size="small">
                    联系管理员
                  </Button>
                </Space>
              </div>
            </Space>

            <div style={{ 
              marginTop: '32px', 
              padding: '16px', 
              background: '#f6f8fa', 
              borderRadius: '8px',
              textAlign: 'center'
            }}>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Login;
