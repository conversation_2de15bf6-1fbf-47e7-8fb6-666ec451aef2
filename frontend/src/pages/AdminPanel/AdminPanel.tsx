import React from 'react';
import { Card, Row, Col, Typography, Statistic, Table, Tag } from 'antd';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { UserOutlined, MedicineBoxOutlined, FileTextOutlined, WarningOutlined } from '@ant-design/icons';

const { Title } = Typography;

const AdminPanel: React.FC = () => {
  const data = [
    { name: '朝阳区', 接种率: 96.5 },
    { name: '海淀区', 接种率: 94.2 },
    { name: '西城区', 接种率: 98.1 },
    { name: '东城区', 接种率: 95.8 },
  ];

  const columns = [
    { title: '机构名称', dataIndex: 'name', key: 'name' },
    { title: '类型', dataIndex: 'type', key: 'type', render: (type: string) => <Tag>{type}</Tag> },
    { title: '状态', dataIndex: 'status', key: 'status', render: (status: string) => <Tag color="green">{status}</Tag> },
  ];

  const institutionData = [
    { key: '1', name: '朝阳医院接种点', type: '医院', status: '正常' },
    { key: '2', name: '海淀区疾控中心', type: '疾控', status: '正常' },
    { key: '3', name: '西城社区卫生服务中心', type: '社区', status: '正常' },
  ];

  return (
    <div>
      <Title level={2}>跨区域数据协同与监管审计</Title>
      
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic title="接入机构" value={156} prefix={<UserOutlined />} />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic title="疫苗批次" value={2340} prefix={<MedicineBoxOutlined />} />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic title="服务记录" value={45678} prefix={<FileTextOutlined />} />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic title="预警事件" value={12} prefix={<WarningOutlined />} />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="各区接种率统计">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="接种率" fill="#1890ff" />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="接入机构管理">
            <Table columns={columns} dataSource={institutionData} pagination={false} size="small" />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default AdminPanel;
