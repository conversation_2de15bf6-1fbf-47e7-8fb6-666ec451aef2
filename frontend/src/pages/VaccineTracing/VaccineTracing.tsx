import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Input,
  Steps,
  Timeline,
  Tag,
  Space,
  Typography,
  Alert,
  Descriptions,
  Modal,
  Table,
  Divider,
  Result,
  Spin,
} from 'antd';
import {
  ScanOutlined,
  SearchOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  EnvironmentOutlined,
  ClockCircleOutlined,
  UserOutlined,
  MedicineBoxOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text } = Typography;
const { Step } = Steps;

interface VaccineTrace {
  id: string;
  batchNumber: string;
  vaccineName: string;
  manufacturer: string;
  productionDate: string;
  expiryDate: string;
  status: 'valid' | 'expired' | 'recalled' | 'suspicious';
  temperature: number;
  traceSteps: TraceStep[];
}

interface TraceStep {
  step: number;
  location: string;
  operator: string;
  timestamp: string;
  temperature: number;
  status: string;
  description: string;
}

const VaccineTracing: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [scanModalVisible, setScanModalVisible] = useState(false);
  const [traceResult, setTraceResult] = useState<VaccineTrace | null>(null);
  const [searchValue, setSearchValue] = useState('');

  // 模拟疫苗追溯数据
  const mockTraceData: VaccineTrace = {
    id: 'VT20240115001',
    batchNumber: 'HBV20240101-A001',
    vaccineName: '乙型肝炎疫苗',
    manufacturer: '北京生物制品研究所',
    productionDate: '2024-01-01',
    expiryDate: '2025-01-01',
    status: 'valid',
    temperature: 4.2,
    traceSteps: [
      {
        step: 1,
        location: '北京生物制品研究所',
        operator: '生产部门',
        timestamp: '2024-01-01 10:00:00',
        temperature: 4.0,
        status: '生产完成',
        description: '疫苗生产完成，质检合格',
      },
      {
        step: 2,
        location: '北京市疾控中心',
        operator: '张三',
        timestamp: '2024-01-02 14:30:00',
        temperature: 3.8,
        status: '入库',
        description: '疫苗入库，温度正常',
      },
      {
        step: 3,
        location: '朝阳区疾控中心',
        operator: '李四',
        timestamp: '2024-01-05 09:15:00',
        temperature: 4.1,
        status: '转运',
        description: '疫苗转运至区级疾控中心',
      },
      {
        step: 4,
        location: '朝阳医院接种点',
        operator: '王五',
        timestamp: '2024-01-10 16:20:00',
        temperature: 4.3,
        status: '配送',
        description: '疫苗配送至接种点',
      },
      {
        step: 5,
        location: '朝阳医院接种点',
        operator: '赵六',
        timestamp: '2024-01-15 11:45:00',
        temperature: 4.2,
        status: '待接种',
        description: '疫苗准备接种，温度监控正常',
      },
    ],
  };

  const handleScan = () => {
    setScanModalVisible(true);
  };

  const handleSearch = () => {
    if (!searchValue.trim()) {
      return;
    }
    
    setLoading(true);
    // 模拟API调用
    setTimeout(() => {
      setTraceResult(mockTraceData);
      setLoading(false);
    }, 1500);
  };

  const handleVerifyVaccination = () => {
    Modal.confirm({
      title: '确认接种',
      content: '确认要为儿童接种此疫苗吗？系统将记录接种信息到区块链。',
      onOk() {
        Modal.success({
          title: '接种确认成功',
          content: '疫苗接种信息已成功记录到区块链，交易哈希：0x1234...abcd',
        });
      },
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'valid': return 'green';
      case 'expired': return 'red';
      case 'recalled': return 'red';
      case 'suspicious': return 'orange';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'valid': return '有效';
      case 'expired': return '已过期';
      case 'recalled': return '已召回';
      case 'suspicious': return '可疑';
      default: return '未知';
    }
  };

  const getTemperatureStatus = (temp: number) => {
    if (temp >= 2 && temp <= 8) {
      return { color: 'green', text: '正常' };
    } else if (temp > 8 || temp < 2) {
      return { color: 'red', text: '异常' };
    }
    return { color: 'orange', text: '警告' };
  };

  const traceColumns: ColumnsType<TraceStep> = [
    {
      title: '步骤',
      dataIndex: 'step',
      key: 'step',
      width: 60,
      render: (step) => (
        <div style={{ 
          width: 24, 
          height: 24, 
          borderRadius: '50%', 
          backgroundColor: '#1890ff',
          color: 'white',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '12px'
        }}>
          {step}
        </div>
      ),
    },
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 150,
      render: (timestamp) => (
        <Space direction="vertical" size={0}>
          <Text>{timestamp.split(' ')[0]}</Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {timestamp.split(' ')[1]}
          </Text>
        </Space>
      ),
    },
    {
      title: '地点',
      dataIndex: 'location',
      key: 'location',
      render: (location) => (
        <Space>
          <EnvironmentOutlined />
          {location}
        </Space>
      ),
    },
    {
      title: '操作人',
      dataIndex: 'operator',
      key: 'operator',
      render: (operator) => (
        <Space>
          <UserOutlined />
          {operator}
        </Space>
      ),
    },
    {
      title: '温度',
      dataIndex: 'temperature',
      key: 'temperature',
      width: 80,
      render: (temp) => {
        const tempStatus = getTemperatureStatus(temp);
        return (
          <Tag color={tempStatus.color}>
            {temp}°C
          </Tag>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color="blue">{status}</Tag>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
  ];

  return (
    <div>
      <Title level={2}>疫苗全流程精准追溯与核验</Title>
      
      {/* 扫码和搜索区域 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} md={12}>
            <Space.Compact style={{ width: '100%' }}>
              <Input
                placeholder="请输入疫苗批次号或追溯码"
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                onPressEnter={handleSearch}
              />
              <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                查询
              </Button>
            </Space.Compact>
          </Col>
          <Col xs={24} md={12}>
            <Space>
              <Button icon={<ScanOutlined />} onClick={handleScan}>
                扫码追溯
              </Button>
              <Button type="dashed">
                批量导入
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 追溯结果 */}
      {loading && (
        <Card>
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>正在查询疫苗追溯信息...</div>
          </div>
        </Card>
      )}

      {traceResult && !loading && (
        <>
          {/* 疫苗基本信息 */}
          <Card title="疫苗基本信息" style={{ marginBottom: 16 }}>
            <Row gutter={[16, 16]}>
              <Col xs={24} lg={16}>
                <Descriptions column={2} bordered>
                  <Descriptions.Item label="疫苗名称">
                    <Space>
                      <MedicineBoxOutlined />
                      {traceResult.vaccineName}
                    </Space>
                  </Descriptions.Item>
                  <Descriptions.Item label="批次号">
                    <Text code>{traceResult.batchNumber}</Text>
                  </Descriptions.Item>
                  <Descriptions.Item label="生产厂家">
                    {traceResult.manufacturer}
                  </Descriptions.Item>
                  <Descriptions.Item label="生产日期">
                    {traceResult.productionDate}
                  </Descriptions.Item>
                  <Descriptions.Item label="有效期至">
                    {traceResult.expiryDate}
                  </Descriptions.Item>
                  <Descriptions.Item label="当前温度">
                    <Tag color={getTemperatureStatus(traceResult.temperature).color}>
                      {traceResult.temperature}°C
                    </Tag>
                  </Descriptions.Item>
                </Descriptions>
              </Col>
              <Col xs={24} lg={8}>
                <div style={{ textAlign: 'center' }}>
                  {traceResult.status === 'valid' ? (
                    <Result
                      icon={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                      title="疫苗状态正常"
                      subTitle="该疫苗可以安全使用"
                      extra={
                        <Button type="primary" onClick={handleVerifyVaccination}>
                          确认接种
                        </Button>
                      }
                    />
                  ) : (
                    <Result
                      icon={<CloseCircleOutlined style={{ color: '#ff4d4f' }} />}
                      title="疫苗状态异常"
                      subTitle={`状态：${getStatusText(traceResult.status)}`}
                      extra={
                        <Button danger>
                          禁止使用
                        </Button>
                      }
                    />
                  )}
                </div>
              </Col>
            </Row>
          </Card>

          {/* 温度监控预警 */}
          {traceResult.temperature < 2 || traceResult.temperature > 8 ? (
            <Alert
              message="温度异常警告"
              description={`当前疫苗温度为 ${traceResult.temperature}°C，超出安全范围（2-8°C），请立即处理！`}
              type="error"
              showIcon
              style={{ marginBottom: 16 }}
            />
          ) : null}

          {/* 追溯流程 */}
          <Card title="追溯流程" style={{ marginBottom: 16 }}>
            <Timeline mode="left">
              {traceResult.traceSteps.map((step, index) => {
                const tempStatus = getTemperatureStatus(step.temperature);
                return (
                  <Timeline.Item
                    key={index}
                    dot={
                      <div style={{
                        width: 24,
                        height: 24,
                        borderRadius: '50%',
                        backgroundColor: '#1890ff',
                        color: 'white',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '12px'
                      }}>
                        {step.step}
                      </div>
                    }
                  >
                    <Card size="small" style={{ marginBottom: 8 }}>
                      <Row gutter={16}>
                        <Col span={18}>
                          <Space direction="vertical" size={4}>
                            <Text strong>{step.status}</Text>
                            <Space>
                              <EnvironmentOutlined />
                              <Text>{step.location}</Text>
                            </Space>
                            <Space>
                              <UserOutlined />
                              <Text>操作人：{step.operator}</Text>
                            </Space>
                            <Text type="secondary">{step.description}</Text>
                          </Space>
                        </Col>
                        <Col span={6} style={{ textAlign: 'right' }}>
                          <Space direction="vertical" size={4} style={{ alignItems: 'flex-end' }}>
                            <Text type="secondary">
                              <ClockCircleOutlined /> {step.timestamp}
                            </Text>
                            <Tag color={tempStatus.color}>
                              温度：{step.temperature}°C
                            </Tag>
                          </Space>
                        </Col>
                      </Row>
                    </Card>
                  </Timeline.Item>
                );
              })}
            </Timeline>
          </Card>

          {/* 详细追溯表格 */}
          <Card title="详细追溯记录">
            <Table
              columns={traceColumns}
              dataSource={traceResult.traceSteps}
              rowKey="step"
              pagination={false}
              size="small"
            />
          </Card>
        </>
      )}

      {/* 扫码模态框 */}
      <Modal
        title="扫描疫苗追溯码"
        open={scanModalVisible}
        onCancel={() => setScanModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setScanModalVisible(false)}>
            取消
          </Button>
        ]}
        width={500}
      >
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <div style={{
            width: 300,
            height: 300,
            border: '2px dashed #d9d9d9',
            borderRadius: 8,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto',
            backgroundColor: '#fafafa'
          }}>
            <ScanOutlined style={{ fontSize: 48, color: '#1890ff', marginBottom: 16 }} />
            <Text>请将疫苗包装上的二维码对准摄像头</Text>
            <Text type="secondary" style={{ marginTop: 8 }}>
              系统将自动识别并查询追溯信息
            </Text>
          </div>
          <Button 
            type="primary" 
            style={{ marginTop: 24 }}
            onClick={() => {
              setScanModalVisible(false);
              setSearchValue('HBV20240101-A001');
              handleSearch();
            }}
          >
            模拟扫码成功
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default VaccineTracing;
