import React from 'react';
import { Card, Row, Col, Typography, Button, Space, Timeline, Tag, Avatar, List } from 'antd';
import { MobileOutlined, QrcodeOutlined, BellOutlined, SafetyCertificateOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

const ParentPortal: React.FC = () => {
  return (
    <div>
      <Title level={2}>家长端授权查询与智能提醒</Title>
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="移动端功能" extra={<MobileOutlined />}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button type="primary" block icon={<QrcodeOutlined />}>
                生成授权二维码
              </Button>
              <Button block icon={<BellOutlined />}>
                智能提醒设置
              </Button>
              <Button block icon={<SafetyCertificateOutlined />}>
                数字签名管理
              </Button>
            </Space>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="最近提醒">
            <Timeline>
              <Timeline.Item color="blue">
                <Text>张小明需要接种脊髓灰质炎疫苗第2剂</Text>
                <br />
                <Text type="secondary">预约时间：2024-01-20</Text>
              </Timeline.Item>
              <Timeline.Item color="green">
                <Text>李小红体检报告已生成</Text>
                <br />
                <Text type="secondary">2024-01-15</Text>
              </Timeline.Item>
            </Timeline>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default ParentPortal;
