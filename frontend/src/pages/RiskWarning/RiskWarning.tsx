import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Typography,
  Alert,
  Table,
  Tag,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Timeline,
  Badge,
  Statistic,
} from 'antd';
import {
  WarningOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  SendOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text } = Typography;
const { Option } = Select;

interface RiskEvent {
  key: string;
  id: string;
  type: 'vaccine_quality' | 'temperature' | 'adverse_reaction' | 'batch_recall';
  title: string;
  description: string;
  severity: 'high' | 'medium' | 'low';
  status: 'active' | 'processing' | 'resolved';
  affectedCount: number;
  reportTime: string;
  batchNumber?: string;
}

const RiskWarning: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [notifyModalVisible, setNotifyModalVisible] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<RiskEvent | null>(null);
  const [form] = Form.useForm();

  // 模拟风险事件数据
  const mockData: RiskEvent[] = [
    {
      key: '1',
      id: 'RW202401001',
      type: 'temperature',
      title: '疫苗温度异常',
      description: '批次号 HBV20240101-A001 在运输过程中温度超出安全范围',
      severity: 'high',
      status: 'active',
      affectedCount: 156,
      reportTime: '2024-01-15 14:30:00',
      batchNumber: 'HBV20240101-A001',
    },
    {
      key: '2',
      id: 'RW202401002',
      type: 'adverse_reaction',
      title: '不良反应聚集',
      description: '朝阳区发现3例疑似疫苗不良反应',
      severity: 'medium',
      status: 'processing',
      affectedCount: 3,
      reportTime: '2024-01-14 09:15:00',
    },
    {
      key: '3',
      id: 'RW202401003',
      type: 'batch_recall',
      title: '疫苗批次召回',
      description: '厂家主动召回批次号 IPV20240105-B002',
      severity: 'high',
      status: 'resolved',
      affectedCount: 89,
      reportTime: '2024-01-13 16:45:00',
      batchNumber: 'IPV20240105-B002',
    },
  ];

  const [dataSource, setDataSource] = useState<RiskEvent[]>(mockData);

  const getTypeConfig = (type: string) => {
    const configs = {
      vaccine_quality: { color: 'red', text: '疫苗质量', icon: <ExclamationCircleOutlined /> },
      temperature: { color: 'orange', text: '温度异常', icon: <WarningOutlined /> },
      adverse_reaction: { color: 'purple', text: '不良反应', icon: <ExclamationCircleOutlined /> },
      batch_recall: { color: 'red', text: '批次召回', icon: <WarningOutlined /> },
    };
    return configs[type as keyof typeof configs] || { color: 'default', text: '未知', icon: null };
  };

  const getSeverityConfig = (severity: string) => {
    const configs = {
      high: { color: 'red', text: '高风险' },
      medium: { color: 'orange', text: '中风险' },
      low: { color: 'yellow', text: '低风险' },
    };
    return configs[severity as keyof typeof configs] || { color: 'default', text: '未知' };
  };

  const getStatusConfig = (status: string) => {
    const configs = {
      active: { color: 'red', text: '活跃', icon: <ExclamationCircleOutlined /> },
      processing: { color: 'orange', text: '处理中', icon: <ClockCircleOutlined /> },
      resolved: { color: 'green', text: '已解决', icon: <CheckCircleOutlined /> },
    };
    return configs[status as keyof typeof configs] || { color: 'default', text: '未知', icon: null };
  };

  const columns: ColumnsType<RiskEvent> = [
    {
      title: '事件ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
      render: (text) => <Text code>{text}</Text>,
    },
    {
      title: '事件类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type) => {
        const config = getTypeConfig(type);
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        );
      },
    },
    {
      title: '事件标题',
      dataIndex: 'title',
      key: 'title',
      width: 200,
    },
    {
      title: '风险等级',
      dataIndex: 'severity',
      key: 'severity',
      width: 100,
      render: (severity) => {
        const config = getSeverityConfig(severity);
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '影响人数',
      dataIndex: 'affectedCount',
      key: 'affectedCount',
      width: 100,
      render: (count) => (
        <Badge count={count} style={{ backgroundColor: '#ff4d4f' }} />
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => {
        const config = getStatusConfig(status);
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        );
      },
    },
    {
      title: '报告时间',
      dataIndex: 'reportTime',
      key: 'reportTime',
      width: 150,
      render: (time) => (
        <Space direction="vertical" size={0}>
          <Text>{time.split(' ')[0]}</Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {time.split(' ')[1]}
          </Text>
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          {record.status === 'active' && (
            <Button
              type="text"
              icon={<SendOutlined />}
              onClick={() => handleNotify(record)}
            >
              通知
            </Button>
          )}
        </Space>
      ),
    },
  ];

  const handleViewDetail = (record: RiskEvent) => {
    setSelectedEvent(record);
  };

  const handleNotify = (record: RiskEvent) => {
    setSelectedEvent(record);
    setNotifyModalVisible(true);
  };

  const handleNotifyOk = async () => {
    try {
      const values = await form.validateFields();
      console.log('Notification sent:', values);
      setNotifyModalVisible(false);
      Modal.success({
        title: '通知发送成功',
        content: `已向 ${selectedEvent?.affectedCount} 位相关人员发送预警通知`,
      });
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  return (
    <div>
      <Title level={2}>风险预警与应急处置管理</Title>
      
      {/* 统计概览 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="活跃预警"
              value={dataSource.filter(item => item.status === 'active').length}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<WarningOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="处理中"
              value={dataSource.filter(item => item.status === 'processing').length}
              valueStyle={{ color: '#faad14' }}
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="已解决"
              value={dataSource.filter(item => item.status === 'resolved').length}
              valueStyle={{ color: '#52c41a' }}
              prefix={<CheckCircleOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="影响总人数"
              value={dataSource.reduce((sum, item) => sum + item.affectedCount, 0)}
              prefix={<ExclamationCircleOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 紧急预警 */}
      {dataSource.some(item => item.status === 'active' && item.severity === 'high') && (
        <Alert
          message="紧急预警"
          description="发现高风险事件，请立即处理！"
          type="error"
          showIcon
          closable
          style={{ marginBottom: 16 }}
        />
      )}

      <Row gutter={[16, 16]}>
        {/* 风险事件列表 */}
        <Col xs={24} lg={16}>
          <Card title="风险事件列表">
            <Table
              columns={columns}
              dataSource={dataSource}
              loading={loading}
              scroll={{ x: 1000 }}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
              }}
            />
          </Card>
        </Col>

        {/* 处理时间线 */}
        <Col xs={24} lg={8}>
          <Card title="最近处理记录">
            <Timeline>
              <Timeline.Item color="red">
                <Text strong>温度异常预警触发</Text>
                <br />
                <Text type="secondary">2024-01-15 14:30</Text>
                <br />
                <Text>批次 HBV20240101-A001</Text>
              </Timeline.Item>
              <Timeline.Item color="orange">
                <Text strong>开始应急响应</Text>
                <br />
                <Text type="secondary">2024-01-15 14:35</Text>
                <br />
                <Text>通知相关机构和家长</Text>
              </Timeline.Item>
              <Timeline.Item color="blue">
                <Text strong>精准定位完成</Text>
                <br />
                <Text type="secondary">2024-01-15 14:40</Text>
                <br />
                <Text>找到156名受影响儿童</Text>
              </Timeline.Item>
              <Timeline.Item color="green">
                <Text strong>批次召回处理完成</Text>
                <br />
                <Text type="secondary">2024-01-13 18:00</Text>
                <br />
                <Text>批次 IPV20240105-B002</Text>
              </Timeline.Item>
            </Timeline>
          </Card>
        </Col>
      </Row>

      {/* 通知发送模态框 */}
      <Modal
        title="发送预警通知"
        open={notifyModalVisible}
        onOk={handleNotifyOk}
        onCancel={() => setNotifyModalVisible(false)}
        width={600}
        destroyOnClose
      >
        <Form form={form} layout="vertical">
          <Form.Item label="事件信息">
            <Card size="small">
              <Text strong>{selectedEvent?.title}</Text>
              <br />
              <Text>{selectedEvent?.description}</Text>
              <br />
              <Text type="secondary">影响人数：{selectedEvent?.affectedCount}</Text>
            </Card>
          </Form.Item>
          
          <Form.Item
            name="notifyType"
            label="通知方式"
            rules={[{ required: true, message: '请选择通知方式' }]}
          >
            <Select mode="multiple" placeholder="请选择通知方式">
              <Option value="app">App推送</Option>
              <Option value="sms">短信通知</Option>
              <Option value="email">邮件通知</Option>
              <Option value="phone">电话通知</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="urgency"
            label="紧急程度"
            rules={[{ required: true, message: '请选择紧急程度' }]}
          >
            <Select placeholder="请选择紧急程度">
              <Option value="immediate">立即通知</Option>
              <Option value="urgent">紧急通知</Option>
              <Option value="normal">普通通知</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="message"
            label="通知内容"
            rules={[{ required: true, message: '请输入通知内容' }]}
          >
            <Input.TextArea
              rows={4}
              placeholder="请输入详细的通知内容和应急指引"
              defaultValue={`紧急通知：${selectedEvent?.title}\n\n${selectedEvent?.description}\n\n请立即联系相关医疗机构进行处理。`}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default RiskWarning;
