import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import 'dayjs/locale/zh-cn';

// 导入页面组件
import Layout from './components/Layout/Layout';
import Dashboard from './pages/Dashboard/Dashboard';
import ChildManagement from './pages/ChildManagement/ChildManagement';
import VaccineTracing from './pages/VaccineTracing/VaccineTracing';
import HealthRecords from './pages/HealthRecords/HealthRecords';
import ParentPortal from './pages/ParentPortal/ParentPortal';
import AdminPanel from './pages/AdminPanel/AdminPanel';
import RiskWarning from './pages/RiskWarning/RiskWarning';
import Login from './pages/Auth/Login';

// 主题配置
const theme = {
  token: {
    colorPrimary: '#1890ff',
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#ff4d4f',
    borderRadius: 8,
    fontSize: 14,
  },
};

function App() {
  return (
    <ConfigProvider locale={zhCN} theme={theme}>
      <Router>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="/" element={<Layout />}>
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={<Dashboard />} />
            <Route path="children" element={<ChildManagement />} />
            <Route path="vaccines" element={<VaccineTracing />} />
            <Route path="records" element={<HealthRecords />} />
            <Route path="parent" element={<ParentPortal />} />
            <Route path="admin" element={<AdminPanel />} />
            <Route path="warnings" element={<RiskWarning />} />
          </Route>
        </Routes>
      </Router>
    </ConfigProvider>
  );
}

export default App;
